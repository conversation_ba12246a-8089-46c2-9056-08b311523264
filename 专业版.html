<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>笔尖传奇 - 专业创作界面 (完整优化版)</title>
    <style>
        /* --- 1. 全局与动态配色系统 --- */
        :root {
            /* 默认皮肤 (护眼蓝灰) */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-panel-secondary: #F3F5F8;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --gutter-color: transparent;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --shadow-color-heavy: rgba(0, 0, 0, 0.12);
            /* 主题色 */
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --secondary-color: #8696A7;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --warning-color: #FF6B6B;
            --warning-color-hover: #FF5252;
            --info-color: #63B3ED;
            --info-color-hover: #4299E1;
            /* 内容区专用色 */
            --content-header-bg: #E8F2FF;
            --content-header-color: #2B5797;
            /* 布局尺寸 */
            --font-size-base: 16px;
            --font-size-sm: 14px;
            --font-size-lg: 18px;
            --header-height: 52px;
            --sidebar-width: 80px;
            --sidebar-collapsed-width: 4px;
        }

        [data-theme="green-leaf"] {
            --bg-main: #EFF3EF;
            --bg-panel: #F8FAF8;
            --bg-panel-secondary: #F0F3F0;
            --bg-content: #F5F8F5;
            --text-dark: #3E4A3E;
            --text-light: #6B7C6B;
            --border-color: #E0E6E0;
            --primary-color: #6A9C89;
            --primary-color-hover: #5A8C79;
            --secondary-color: #8A9B94;
            --accent-color: #E99469;
            --accent-color-hover: #D98459;
            --shadow-color: rgba(106, 156, 137, 0.08);
            --content-header-bg: #E5F2E9;
            --content-header-color: #3A6B4F;
        }

        [data-theme="sepia"] {
            --bg-main: #FBF0D9;
            --bg-panel: #FAF4E8;
            --bg-panel-secondary: #F6ECDA;
            --bg-content: #FAF4E8;
            --text-dark: #5C4B33;
            --text-light: #8B7355;
            --border-color: #EAE0C8;
            --primary-color: #A67B5B;
            --primary-color-hover: #966B4B;
            --secondary-color: #B0A08D;
            --accent-color: #5D9CEC;
            --accent-color-hover: #4A89E2;
            --shadow-color: rgba(166, 123, 91, 0.1);
            --content-header-bg: #F4E6D4;
            --content-header-color: #7A5A3A;
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-panel-secondary: #252E3E;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --secondary-color: #3B475C;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --content-header-bg: #3A4558;
            --content-header-color: #CBD5E0;
        }

        /* --- 2. 基础与布局 --- */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            color: var(--text-dark);
            font-size: var(--font-size-base);
            line-height: 1.7;
            -webkit-font-smoothing: antialiased;
            display: flex;
            height: 100vh;
            overflow: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 左侧导航栏 (可自动隐藏) --- */
        .sidebar-wrapper {
            position: relative;
            width: var(--sidebar-width);
            flex-shrink: 0;
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-wrapper.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar {
            position: absolute;
            left: 0;
            top: 0;
            width: var(--sidebar-width);
            height: 100%;
            background: var(--bg-panel);
            box-shadow: 2px 0 8px var(--shadow-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 0;
            z-index: 100;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        /* 隐藏状态 */
        .sidebar-wrapper.collapsed .sidebar {
            transform: translateX(calc(-1 * var(--sidebar-width) + var(--sidebar-collapsed-width)));
        }

        /* 鼠标感应区域 - 修复：使用更大的热区 */
        .sidebar-trigger {
            position: fixed;
            left: 0;
            top: 0;
            width: 20px; /* 减小宽度，避免干扰内容 */
            height: 100%;
            z-index: 101;
        }

        /* 导航栏展开时的热区扩大 */
        .sidebar-wrapper:not(.collapsed) .sidebar-trigger {
            width: calc(var(--sidebar-width) + 20px);
        }

        /* 导航栏内容淡入淡出 */
        .sidebar-content {
            opacity: 1;
            transition: opacity 0.2s;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
        }
        .sidebar-wrapper.collapsed .sidebar-content {
            opacity: 0;
            pointer-events: none;
        }

        /* 用户头像 - 调整大小 */
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px var(--shadow-color);
            transition: transform 0.2s;
            flex-shrink: 0;
        }
        .user-avatar:hover {
            transform: scale(1.05);
        }
        .user-avatar img { 
            width: 100%; 
            height: 100%; 
            object-fit: cover; 
        }

        /* 导航组 */
        .nav-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            width: 100%;
        }

        /* 导航项 */
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            color: var(--text-light);
            transition: all 0.2s ease;
            padding: 8px;
            border-radius: 10px;
            width: 60px;
        }
        .nav-item:hover {
            color: var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }
        .nav-item.active {
            color: var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }
        .nav-item-icon {
            font-size: 20px;
            width: 24px;
            height: 24px;
            fill: currentColor;
        }
        .nav-item-text {
            font-size: 11px;
            font-weight: 500;
        }
        .sidebar-footer {
            margin-top: auto;
        }

        /* --- 主应用容器 --- */
        .app-container {
            display: flex; 
            gap: 0; 
            width: 100%; 
            height: 100vh;
            flex-grow: 1;
        }
        
        .panel {
            background-color: var(--bg-panel); 
            box-shadow: 0 2px 8px var(--shadow-color);
            display: flex; 
            flex-direction: column; 
            overflow: hidden; 
            height: 100%; 
            transition: background-color 0.3s;
        }

        /* --- 3. 统一标题栏样式 --- */
        .panel-header {
            height: var(--header-height);
            padding: 0 20px;
            font-weight: 600;
            font-size: var(--font-size-lg);
            flex-shrink: 0;
            display: flex;
            align-items: center;
            transition: background-color 0.3s, color 0.3s;
            background-color: var(--primary-color);
            color: var(--text-on-primary);
            border-bottom: 1px solid var(--border-color);
        }

        /* 章节管理头部 - 对齐按钮 */
        .management-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            width: 100%;
        }
        
        .management-header .btn-group {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        /* 作品名称显示和编辑 */
        .work-title {
            font-size: var(--font-size-sm);
            font-weight: 500;
            margin-right: 10px;
            opacity: 0.9;
            cursor: pointer;
            padding: 2px 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .work-title:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .work-title-input {
            font-size: var(--font-size-sm);
            font-weight: 500;
            margin-right: 10px;
            padding: 2px 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.15);
            color: var(--text-on-primary);
            font-family: inherit;
        }
        
        /* 自动保存状态 */
        .auto-save-status {
            font-size: 12px;
            opacity: 0.7;
            margin-left: 10px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .btn-new { 
            background-color: rgba(255,255,255,0.2); 
            color: var(--text-on-primary); 
            border: none; 
            border-radius: 6px; 
            padding: 6px 12px; 
            font-size: var(--font-size-sm); 
            cursor: pointer; 
            transition: background-color 0.2s;
        }
        .btn-new:hover { 
            background-color: rgba(255,255,255,0.3); 
        }

        /* 本章内容专用标题栏 */
        .content-header {
            background-color: var(--content-header-bg);
            color: var(--content-header-color);
            border-bottom: 1px solid var(--border-color);
            justify-content: space-between;
        }

        /* 章节编号编辑样式 */
        .chapter-number {
            font-weight: 600;
            cursor: pointer;
            padding: 2px 6px;
            border-radius: 4px;
            transition: background-color 0.2s;
            margin-right: 8px;
        }
        
        .chapter-number:hover {
            background-color: rgba(93, 156, 236, 0.1);
        }
        
        .chapter-number-input {
            font-weight: 600;
            padding: 2px 6px;
            border: 1px solid var(--primary-color);
            border-radius: 4px;
            background: var(--bg-content);
            color: var(--text-dark);
            font-size: var(--font-size-base);
            font-family: inherit;
            width: auto;
            min-width: 80px;
            margin-right: 8px;
        }

        /* 当前章节标题可编辑 */
        .current-chapter-title {
            cursor: pointer;
            padding: 2px 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .current-chapter-title:hover {
            background-color: rgba(43, 87, 151, 0.1);
        }
        
        .current-chapter-title-input {
            font-size: var(--font-size-lg);
            font-weight: 600;
            padding: 2px 8px;
            border: 1px solid var(--primary-color);
            border-radius: 4px;
            background: var(--bg-content);
            color: var(--content-header-color);
            font-family: inherit;
            width: auto;
            min-width: 200px;
        }

        /* 主工具栏样式 */
        .toolbar {
            height: var(--header-height);
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: var(--primary-color);
            color: var(--text-on-primary);
            flex-shrink: 0;
        }

        .panel-content { 
            padding: 24px; 
            overflow-y: auto; 
            flex-grow: 1; 
        }

        /* 章节列表样式 - 优化拖拽和编辑 */
        .item-list { 
            list-style: none; 
            padding: 0; 
            margin: 0; 
        }
        
        .chapter-item { 
            padding: 12px 20px; 
            cursor: move; 
            border-bottom: 1px solid var(--border-color); 
            transition: all 0.2s; 
            font-size: var(--font-size-base); 
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            background-color: var(--bg-panel);
            user-select: none;
        }
        
        .chapter-item:last-child { 
            border-bottom: none; 
        }
        
        .chapter-item:hover { 
            background-color: var(--bg-panel-secondary); 
        }
        
        .chapter-item.active { 
            background-color: var(--primary-color); 
            font-weight: 600; 
            color: var(--text-on-primary);
        }
        
        /* 拖动中的章节样式 - 修复问题3：改进拖拽视觉效果 */
        .chapter-item.dragging {
            opacity: 0.5;
            transform: scale(0.95);
            cursor: grabbing;
            z-index: 1000;
        }

        .chapter-item.drag-over-top {
            border-top: 3px solid var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }

        .chapter-item.drag-over-bottom {
            border-bottom: 3px solid var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }
        
        /* 章节标题样式 - 不可编辑 */
        .chapter-title-wrapper {
            flex-grow: 1;
            display: flex;
            align-items: center;
        }
        
        .chapter-title {
            flex-grow: 1;
            cursor: pointer;
        }
        
        /* 章节操作按钮 */
        .chapter-actions {
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.2s;
        }
        
        .chapter-item:hover .chapter-actions {
            opacity: 1;
        }
        
        .chapter-item.active .chapter-actions {
            opacity: 1;
        }
        
        .chapter-action-btn {
            width: 24px;
            height: 24px;
            border: none;
            border-radius: 4px;
            background: rgba(255,255,255,0.2);
            color: var(--text-dark);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        
        .chapter-item.active .chapter-action-btn {
            color: var(--text-on-primary);
        }
        
        .chapter-action-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }
        
        /* --- 4. 中间工作区 & 悬浮层叠布局 --- */
        .center-panel {
            display: flex; 
            flex-direction: column; 
            background-color: transparent; 
            box-shadow: none; 
            padding: 0; 
            gap: 0; 
            position: relative;
            flex-grow: 1;
        }
        
        /* 中间内容区域包装器 */
        .center-content-wrapper { 
            flex-grow: 1; 
            display: flex; 
            flex-direction: column; 
            gap: 0; 
            overflow: hidden;
            position: relative;
        }
        
        /* 主工具栏按钮组 */
        .toolbar .btn-group { 
            display: flex; 
            gap: 10px; 
        }
        .toolbar button {
            background-color: rgba(255,255,255,0.2);
            color: var(--text-on-primary);
            border: none;
            cursor: pointer;
            font-size: var(--font-size-sm);
            padding: 8px 14px;
            border-radius: 8px;
            transition: background-color 0.2s, transform 0.2s;
            font-weight: 500;
        }
        .toolbar button:hover { 
            background-color: rgba(255,255,255,0.35);
            transform: translateY(-1px);
        }
        
        /* 显示/隐藏按钮 */
        #toggle-io-btn {
            background-color: var(--warning-color);
            padding: 10px 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            font-weight: 600;
        }
        #toggle-io-btn:hover {
            background-color: var(--warning-color-hover);
        }
        
        /* 模式切换按钮 */
        .mode-switch-btn {
            background-color: var(--info-color) !important;
            padding: 10px 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .mode-switch-btn:hover {
            background-color: var(--info-color-hover) !important;
        }

        /* 返回书架按钮 - 新增样式 */
        .btn-back-shelf {
            background-color: var(--secondary-color) !important;
            padding: 10px 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
            margin-right: 10px;
        }
        
        .btn-back-shelf:hover {
            background-color: var(--primary-color-hover) !important;
            transform: translateY(-1px);
        }

        /* 主题选择器 */
        .theme-selector {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        /* 基础层: 本章内容 */
        #chapter-content-wrapper {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            background: var(--bg-content);
            overflow: hidden;
            transition: background-color 0.3s;
        }
        
        /* 编辑区容器 - 修复问题1：确保章节标题栏不变化 */
        .edit-area-container {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 75%;
            display: flex;
            flex-direction: column;
            transform: translateY(100%);
            opacity: 0;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s;
            z-index: 5;
            pointer-events: none;
            min-height: 300px;
        }

        .edit-area-container.show {
            transform: translateY(0);
            opacity: 1;
            pointer-events: auto;
        }

        /* 悬浮层: 输入和输出框容器 */
        .floating-input-output-container {
            display: flex;
            flex-direction: column;
            box-shadow: 0 -8px 20px var(--shadow-color-heavy);
            flex: 1;
            min-height: 160px;
            height: 60%;
        }
        .text-area-wrapper {
            display: flex;
            flex-direction: column;
            background: var(--bg-panel);
            overflow: hidden;
            flex: 1;
            min-height: 80px;
            transition: background-color 0.3s;
        }

        /* 编辑区内的文本区域特殊样式 */
        .edit-area-container .text-area-wrapper {
            height: 50%;
        }
        textarea {
            flex-grow: 1; 
            width: 100%; 
            border: none; 
            padding: 20px; 
            font-size: var(--font-size-base);
            line-height: 1.7; 
            resize: none; 
            color: var(--text-dark); 
            background-color: transparent;
        }
        textarea:focus { 
            outline: none; 
        }
        
        /* 继续追问弹窗样式 - 修复问题6：支持独立关闭，默认显示完整 */
        .follow-up-modal {
            height: 240px;
            background: var(--bg-panel);
            border-top: 2px solid var(--primary-color);
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            position: relative;
            transition: height 0.3s ease, opacity 0.3s ease;
            resize: none !important; /* 完全禁用拖拽调整 */
        }

        .follow-up-modal.hidden {
            height: 0;
            opacity: 0;
            overflow: hidden;
        }
        
        .follow-up-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--primary-color);
            color: var(--text-on-primary);
        }
        
        .follow-up-title {
            font-weight: 600;
            font-size: var(--font-size-base);
        }
        
        .follow-up-close {
            width: 24px;
            height: 24px;
            border: none;
            background: transparent;
            color: var(--text-on-primary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .follow-up-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .follow-up-content {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .follow-up-input {
            width: 100%;
            height: 100%;
            min-height: 80px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px;
            font-size: var(--font-size-base);
            resize: none !important;
            background: var(--bg-content);
            color: var(--text-dark);
            font-family: inherit;
            overflow-y: auto;
        }

        /* 完全隐藏所有可能的拖拽手柄 */
        .follow-up-modal *::-webkit-resizer,
        .follow-up-modal *::-moz-resizer {
            display: none !important;
        }

        .follow-up-modal textarea::-webkit-resizer,
        .follow-up-modal textarea::-moz-resizer {
            display: none !important;
        }
        
        .follow-up-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }
        
        .follow-up-footer {
            padding: 12px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        
        .follow-up-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 8px;
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .follow-up-btn.primary {
            background: var(--accent-color);
            color: var(--text-on-primary);
        }
        
        .follow-up-btn.primary:hover {
            background: var(--accent-color-hover);
            transform: translateY(-1px);
        }
        
        .follow-up-btn.cancel {
            background: var(--bg-panel-secondary);
            color: var(--text-dark);
        }
        
        .follow-up-btn.cancel:hover {
            background: var(--bg-content);
        }
        
        /* 底部操作按钮 */
        .action-buttons {
            background-color: var(--bg-panel);
            padding: 12px 20px;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            border-top: 1px solid var(--border-color);
        }
        .btn-secondary {
            background-color: var(--bg-panel);
            color: var(--primary-color);
            border: 1px solid var(--border-color);
            cursor: pointer;
            font-size: var(--font-size-sm);
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.2s;
            font-weight: 500;
        }
        .btn-secondary:hover {
            background-color: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* --- 5. 右侧控制面板 --- */
        #writing-section, #inspiration-section { 
            display: none; 
        }
        body.mode-content #writing-section { 
            display: block; 
        }
        body.mode-brainstorm #inspiration-section { 
            display: block; 
        }

        /* 内容区段标题 */
        .content-section-header { 
            font-size: var(--font-size-lg); 
            font-weight: 600; 
            padding-bottom: 12px; 
            margin-bottom: 16px; 
            border-bottom: 1px solid var(--border-color); 
            margin-top: 28px;
        }
        .content-section-header:first-child {
            margin-top: 0;
        }

        /* 便签管理区域 - 统一样式 */
        .notes-section {
            background-color: var(--bg-panel-secondary);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }
        .notes-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        .notes-header h3 {
            margin: 0;
            font-size: var(--font-size-lg);
        }
        .add-note-btn {
            background-color: var(--accent-color);
            color: var(--text-on-primary);
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .add-note-btn:hover {
            background-color: var(--accent-color-hover);
        }

        /* 通用控件样式 */
        .control-group { 
            margin-bottom: 20px; 
        }
        .control-group label { 
            display: block; 
            font-size: var(--font-size-sm); 
            font-weight: 500; 
            margin-bottom: 10px; 
            color: var(--text-dark); 
        }
        .control-group input[type="text"], 
        .control-group select { 
            width: 100%; 
            padding: 12px; 
            border: 1px solid var(--border-color); 
            border-radius: 8px; 
            background-color: var(--bg-panel); 
            font-size: var(--font-size-sm); 
            color: var(--text-dark);
            transition: all 0.2s;
        }
        .control-group input[type="text"]:focus, 
        .control-group select:focus { 
            outline: none; 
            border-color: var(--primary-color); 
            box-shadow: 0 0 0 3px var(--shadow-color); 
        }
        
        /* 提示词选择器样式 */
        .prompt-selector-wrapper {
            position: relative;
        }
        
        .prompt-selector-display {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--bg-panel);
            font-size: var(--font-size-sm);
            color: var(--text-dark);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s;
        }
        
        .prompt-selector-display:hover {
            border-color: var(--primary-color);
        }
        
        .prompt-manage-btn {
            background-color: var(--accent-color);
            color: var(--text-on-primary);
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            margin-top: 8px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }
        
        .prompt-manage-btn:hover {
            background-color: var(--accent-color-hover);
            transform: translateY(-1px);
        }
        
        /* 多选下拉框样式 */
        .multiselect {
            position: relative;
        }
        .multiselect-display {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--bg-panel);
            font-size: var(--font-size-sm);
            color: var(--text-dark);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .multiselect-display:hover {
            border-color: var(--primary-color);
        }
        .multiselect-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: var(--bg-panel);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-top: 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 4px 12px var(--shadow-color);
            display: none;
        }
        .multiselect.open .multiselect-dropdown {
            display: block;
        }
        .multiselect-option {
            padding: 10px 12px;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .multiselect-option:hover {
            background-color: var(--bg-panel-secondary);
        }
        .multiselect-option input[type="checkbox"] {
            margin: 0;
        }
        
        .generate-button { 
            width: 100%; 
            padding: 14px; 
            background-color: var(--accent-color); 
            color: var(--text-on-primary); 
            border: none; 
            border-radius: 10px; 
            font-size: var(--font-size-lg); 
            font-weight: 600; 
            cursor: pointer; 
            transition: all 0.2s; 
        }
        .generate-button:hover { 
            background-color: var(--accent-color-hover); 
            transform: translateY(-2px); 
        }
        .generate-button-wrapper { 
            padding: 24px; 
            background-color: var(--bg-panel); 
            border-top: 1px solid var(--border-color); 
            flex-shrink: 0; 
            transition: background-color 0.3s, border-color 0.3s; 
        }

        /* Split.js 拖动条 - 完全隐藏 */
        .gutter {
            display: none !important;
        }

        /* 移除左右边框 */
        .left-panel {
            border-right: none !important;
        }
        .right-panel {
            border-left: none !important;
        }

        /* 完全隐藏所有可能的拖拽调整手柄 */
        *::-webkit-resizer {
            display: none !important;
        }

        *::-moz-resizer {
            display: none !important;
        }

        /* 禁用所有textarea的拖拽调整 */
        textarea {
            resize: none !important;
        }

        /* 确保继续追问对话框不显示任何拖拽元素 */
        .follow-up-modal,
        .follow-up-modal *,
        .follow-up-content,
        .follow-up-content * {
            resize: none !important;
        }
        
        /* 通知弹窗样式 */
        .notification {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.9);
            background: var(--bg-panel);
            border-radius: 12px;
            padding: 20px 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            z-index: 2000;
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            pointer-events: none;
        }

        .notification.show {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
            pointer-events: auto;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: var(--font-size-base);
            color: var(--text-dark);
        }

        .notification-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification.success .notification-icon {
            color: var(--accent-color);
        }

        .notification.error .notification-icon {
            color: var(--warning-color);
        }

        .notification.info .notification-icon {
            color: var(--info-color);
        }

        /* 确认对话框样式 - 修复问题3：删除确认弹窗居中 */
        .prompt-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 3000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .prompt-detail-modal.show {
            opacity: 1;
        }

        .prompt-detail-content {
            background: var(--bg-panel);
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .prompt-detail-modal.show .prompt-detail-content {
            transform: scale(1);
        }

        .prompt-detail-body {
            padding: 30px;
            text-align: center;
        }

        .prompt-detail-footer {
            padding: 20px 30px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .prompt-detail-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 8px;
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .prompt-detail-btn.cancel {
            background: var(--bg-panel-secondary);
            color: var(--text-dark);
        }

        .prompt-detail-btn.cancel:hover {
            background: var(--bg-content);
        }

        .prompt-detail-btn.confirm {
            background: var(--warning-color);
            color: var(--text-on-primary);
        }

        .prompt-detail-btn.confirm:hover {
            background: var(--warning-color-hover);
            transform: translateY(-1px);
        }

        /* 拖拽分隔条样式已移除 - 拖动条已隐藏 */

        /* 提示词管理弹窗样式 - 完整复制专业版(3) */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal.show {
            opacity: 1;
        }

        .modal-content {
            background: var(--bg-panel);
            border-radius: 16px;
            width: 90%;
            max-width: 1200px;
            height: 80%;
            max-height: 800px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .modal.show .modal-content {
            transform: scale(1);
        }

        .modal-header {
            padding: 20px 30px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .modal-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }

        .modal-close {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-light);
            border-radius: 6px;
            transition: all 0.2s;
        }

        .modal-close:hover {
            background: var(--bg-panel-secondary);
            color: var(--text-dark);
        }

        .modal-body {
            padding: 24px;
            overflow-y: auto;
            flex-grow: 1;
        }

        /* 标签切换 */
        .modal-tabs {
            display: flex;
            gap: 20px;
            margin-bottom: 24px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 12px;
        }

        .modal-tab {
            padding: 8px 16px;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-light);
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .modal-tab:hover {
            color: var(--text-dark);
        }

        .modal-tab.active {
            color: var(--primary-color);
        }

        .modal-tab.active::after {
            content: '';
            position: absolute;
            bottom: -13px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-color);
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: scaleX(0);
            }
            to {
                transform: scaleX(1);
            }
        }

        .modal-section {
            display: none;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-section-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 16px;
        }

        /* 提示词列表 */
        .prompt-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .prompt-item {
            padding: 16px;
            background: var(--bg-panel-secondary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s;
            opacity: 0;
            transform: translateY(10px);
            animation: promptFadeIn 0.3s ease-out forwards;
        }

        .prompt-item:nth-child(n) {
            animation-delay: calc(0.05s * var(--item-index));
        }

        @keyframes promptFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .prompt-item:hover {
            background: var(--bg-content);
            border-color: var(--primary-color);
        }

        .prompt-info {
            flex-grow: 1;
        }

        .prompt-name {
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 4px;
        }

        .prompt-desc {
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }

        .prompt-actions {
            display: flex;
            gap: 8px;
        }

        .prompt-action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
        }

        .prompt-action-btn.delete {
            background: var(--warning-color);
            color: var(--text-on-primary);
        }

        .prompt-action-btn.delete:hover {
            background: var(--warning-color-hover);
        }

        /* 创建提示词表单 */
        .create-prompt-form {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-size: var(--font-size-sm);
            font-weight: 500;
            color: var(--text-dark);
        }

        .form-input,
        .form-textarea {
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-content);
            color: var(--text-dark);
            font-size: var(--font-size-sm);
            transition: all 0.2s;
        }

        .form-input:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 16px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--text-on-primary);
        }

        .btn-primary:hover {
            background: var(--primary-color-hover);
            transform: translateY(-1px);
        }

        .btn-cancel {
            background: var(--bg-panel-secondary);
            color: var(--text-dark);
        }

        .btn-cancel:hover {
            background: var(--bg-content);
        }

        /* 多级菜单样式 */
        .menu-breadcrumb {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }

        .breadcrumb-item {
            cursor: pointer;
            transition: color 0.2s;
        }

        .breadcrumb-item:hover {
            color: var(--primary-color);
        }

        .breadcrumb-separator {
            color: var(--text-light);
            opacity: 0.5;
        }

        .menu-level {
            display: none;
            animation: menuFadeIn 0.3s ease-out;
        }

        .menu-level.active {
            display: block;
        }

        @keyframes menuFadeIn {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }

        .menu-item {
            padding: 16px;
            background: var(--bg-panel-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .menu-item:hover {
            background: var(--bg-content);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px var(--shadow-color);
        }

        .menu-item-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .menu-item-name {
            font-size: var(--font-size-sm);
            font-weight: 500;
            color: var(--text-dark);
        }

        /* 搜索框样式 */
        .search-container {
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-content);
            font-size: var(--font-size-sm);
            transition: all 0.2s;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        /* 提示词卡片网格 */
        .prompt-cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 16px;
        }

        .prompt-card {
            padding: 16px;
            background: var(--bg-panel-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0;
            transform: translateY(20px);
            animation: cardFadeIn 0.5s ease-out forwards;
        }

        .prompt-card:nth-child(n) {
            animation-delay: calc(0.05s * var(--card-index));
        }

        @keyframes cardFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .prompt-card:hover {
            background: var(--bg-content);
            border-color: var(--primary-color);
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 8px 20px var(--shadow-color);
        }

        .prompt-card.added {
            opacity: 0.6;
            cursor: default;
        }

        .prompt-card.added:hover {
            transform: none;
            box-shadow: none;
        }

        .prompt-card-title {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
        }

        .prompt-card-desc {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            margin-bottom: 12px;
            line-height: 1.5;
        }

        .prompt-card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--text-light);
        }

        .prompt-author {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .prompt-stats {
            display: flex;
            gap: 12px;
        }

        .prompt-stat {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* 级联选择器样式 */
        .cascader-group {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .cascader-select {
            flex: 1;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-content);
            font-size: var(--font-size-sm);
            color: var(--text-dark);
            cursor: pointer;
            transition: all 0.2s;
        }

        .cascader-select:hover {
            border-color: var(--primary-color);
        }

        .cascader-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .cascader-select:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 多选组件样式 - 修复问题4：支持多选操作 */
        .multi-select-container {
            position: relative;
            width: 100%;
        }

        .multi-select-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-content);
            cursor: pointer;
            transition: all 0.2s;
            min-height: 40px;
        }

        .multi-select-display:hover {
            border-color: var(--primary-color);
        }

        .multi-select-display.active {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .multi-select-placeholder {
            color: var(--text-light);
            font-size: var(--font-size-sm);
        }

        .multi-select-selected {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            flex: 1;
        }

        .multi-select-tag {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 2px 8px;
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .multi-select-tag-remove {
            cursor: pointer;
            font-weight: bold;
            opacity: 0.7;
            transition: opacity 0.2s;
        }

        .multi-select-tag-remove:hover {
            opacity: 1;
        }

        .multi-select-arrow {
            color: var(--text-light);
            font-size: 12px;
            transition: transform 0.2s;
            margin-left: 8px;
        }

        .multi-select-display.active .multi-select-arrow {
            transform: rotate(180deg);
        }

        .multi-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--bg-panel);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 4px 12px var(--shadow-color);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        }

        .multi-select-dropdown.show {
            display: block;
            animation: dropdownFadeIn 0.2s ease-out;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .multi-select-option {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .multi-select-option:hover {
            background: var(--bg-panel-secondary);
        }

        .multi-select-option input[type="checkbox"] {
            margin-right: 8px;
            cursor: pointer;
        }

        .multi-select-option label {
            flex: 1;
            cursor: pointer;
            font-size: var(--font-size-sm);
            color: var(--text-dark);
        }

        .multi-select-option.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .multi-select-option.disabled input,
        .multi-select-option.disabled label {
            cursor: not-allowed;
        }

        /* 提示词详情弹窗 */
        .prompt-detail-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0);
            z-index: 1100;
            justify-content: center;
            align-items: center;
            transition: background-color 0.3s ease-out;
        }

        .prompt-detail-modal.show {
            display: flex;
            background-color: rgba(0, 0, 0, 0.6);
        }

        .prompt-detail-content {
            background-color: var(--bg-panel);
            border-radius: 16px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            transform: scale(0.9) translateY(20px);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .prompt-detail-modal.show .prompt-detail-content {
            transform: scale(1) translateY(0);
            opacity: 1;
        }

        .prompt-detail-header {
            padding: 24px 24px 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .prompt-detail-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            margin: 0 0 8px 0;
        }

        .prompt-detail-meta {
            display: flex;
            gap: 16px;
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }

        .prompt-detail-body {
            padding: 24px;
            max-height: 400px;
            overflow-y: auto;
        }

        .prompt-detail-section {
            margin-bottom: 20px;
        }

        .prompt-detail-section:last-child {
            margin-bottom: 0;
        }

        .prompt-detail-label {
            display: block;
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
        }

        .prompt-detail-text {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            line-height: 1.6;
            background: var(--bg-panel-secondary);
            padding: 12px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .prompt-detail-footer {
            padding: 16px 24px 24px;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            border-top: 1px solid var(--border-color);
        }

        .prompt-detail-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .prompt-detail-btn.cancel {
            background: var(--bg-panel-secondary);
            color: var(--text-dark);
        }

        .prompt-detail-btn.cancel:hover {
            background: var(--bg-content);
        }

        .prompt-detail-btn.confirm {
            background: var(--primary-color);
            color: var(--text-on-primary);
        }

        .prompt-detail-btn.confirm:hover {
            background: var(--primary-color-hover);
            transform: translateY(-1px);
        }

        /* 级联选择器样式 */
        .cascader-group {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .cascader-select {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-content);
            font-size: var(--font-size-sm);
        }

        /* 面包屑导航 */
        .menu-breadcrumb {
            margin-bottom: 20px;
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }

        .breadcrumb-item {
            cursor: pointer;
            color: var(--primary-color);
            text-decoration: underline;
        }

        .breadcrumb-item:hover {
            color: var(--primary-color-hover);
        }

        /* 菜单级别 */
        .menu-level {
            display: none;
        }

        .menu-level.active {
            display: block;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
        }

        .menu-item {
            padding: 20px;
            background: var(--bg-panel-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s;
        }

        .menu-item:hover {
            background: var(--bg-content);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .menu-item-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .menu-item-name {
            font-weight: 500;
            color: var(--text-dark);
        }

        /* 我的提示词列表 */
        .prompt-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .prompt-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: var(--bg-panel-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 8px;
            opacity: 0;
            transform: translateX(-20px);
            animation: itemSlideIn 0.3s ease-out forwards;
            animation-delay: calc(0.05s * var(--item-index));
        }

        @keyframes itemSlideIn {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .prompt-info {
            flex: 1;
        }

        .prompt-name {
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 4px;
        }

        .prompt-desc {
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }

        .prompt-actions {
            display: flex;
            gap: 8px;
        }

        .prompt-action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
        }

        .prompt-action-btn.delete {
            background: var(--warning-color);
            color: var(--text-on-primary);
        }

        .prompt-action-btn.delete:hover {
            background: var(--warning-color-hover);
        }

        /* 字号调节和字体更换弹窗样式 */
        .font-dialog {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .font-dialog.show {
            opacity: 1;
        }

        .font-dialog-content {
            background: var(--bg-panel);
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
            transform: scale(0.9);
            transition: transform 0.3s ease;
            max-width: 500px;
            width: 90%;
        }

        .font-dialog.show .font-dialog-content {
            transform: scale(1);
        }

        .font-dialog-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .font-dialog-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }

        .font-dialog-close {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-light);
            border-radius: 6px;
            transition: all 0.2s;
        }

        .font-dialog-close:hover {
            background: var(--bg-panel-secondary);
            color: var(--text-dark);
        }

        .font-dialog-body {
            padding: 24px;
        }

        .font-size-controls {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }

        .font-size-slider {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: var(--bg-panel-secondary);
            outline: none;
            -webkit-appearance: none;
        }

        .font-size-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        .font-size-value {
            min-width: 40px;
            text-align: center;
            font-weight: 500;
            color: var(--text-dark);
        }

        .font-family-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }

        .font-family-item {
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
            background: var(--bg-content);
        }

        .font-family-item:hover {
            border-color: var(--primary-color);
            background: var(--bg-panel-secondary);
        }

        .font-family-item.selected {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: var(--text-on-primary);
        }

        .font-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            margin-bottom: 20px;
        }

        .font-upload-area:hover {
            border-color: var(--primary-color);
            background: var(--bg-panel-secondary);
        }

        .font-upload-area.dragover {
            border-color: var(--primary-color);
            background: var(--bg-panel-secondary);
        }

        /* 搜索和替换弹窗样式 */
        .search-dialog {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .search-dialog.show {
            opacity: 1;
        }

        .search-dialog-content {
            background: var(--bg-panel);
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
            transform: scale(0.9);
            transition: transform 0.3s ease;
            max-width: 600px;
            width: 90%;
        }

        .search-dialog.show .search-dialog-content {
            transform: scale(1);
        }

        /* 历史记录样式 */
        .history-item {
            transition: all 0.2s ease;
        }

        .history-item:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        /* 角色卡和知识卡样式增强 */
        .modal-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }

        .modal-tab {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
            color: var(--text-light);
        }

        .modal-tab:hover {
            color: var(--text-dark);
            background: var(--bg-panel-secondary);
        }

        .modal-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .modal-section {
            display: none;
        }

        .modal-section.active {
            display: block;
        }

        .create-prompt-form {
            max-width: 100%;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 24px;
        }

        /* 按钮样式增强 */
        .btn-secondary {
            background: var(--bg-panel-secondary);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }

        .btn-secondary:hover {
            background: var(--bg-panel);
            border-color: var(--primary-color);
        }

        .btn-cancel {
            background: transparent;
            color: var(--text-light);
            border: 1px solid var(--border-color);
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }

        .btn-cancel:hover {
            background: var(--bg-panel-secondary);
            color: var(--text-dark);
        }

        /* 工具栏按钮间距调整 */
        .toolbar .btn-group {
            gap: 8px;
        }

        .toolbar .btn-group button {
            white-space: nowrap;
            min-width: auto;
            padding: 8px 12px;
        }
    </style>
</head>
<body>
    <!-- 鼠标感应区域 -->
    <div class="sidebar-trigger" id="sidebarTrigger"></div>

    <!-- 左侧导航栏包装器 -->
    <div class="sidebar-wrapper collapsed" id="sidebarWrapper">
        <!-- 左侧导航栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <a href="#" class="user-avatar">
                    <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="User Avatar">
                </a>
                <div class="nav-group">
                    <div class="nav-item" title="首页" onclick="window.location.href='首页.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                        </svg>
                        <span class="nav-item-text">首页</span>
                    </div>
                    <div class="nav-item" title="书架" onclick="window.location.href='书架.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                        </svg>
                        <span class="nav-item-text">书架</span>
                    </div>
                    <div class="nav-item active" title="创意">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14.06,9.02l0.91,0.91L5.66,19.24L4.75,18.33L14.06,9.02 M17.66,3c-0.25,0-0.51,0.1-0.7,0.29l-1.83,1.83l3.75,3.75l1.83-1.83c0.39-0.39,0.39-1.02,0-1.41l-2.34-2.34C18.17,3.09,17.92,3,17.66,3L17.66,3z M12.06,6.19L3,15.25V19.24h3.99l9.06-9.06L12.06,6.19z"/>
                        </svg>
                        <span class="nav-item-text">创意</span>
                    </div>
                    <div class="nav-item" title="对话">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                        </svg>
                        <span class="nav-item-text">对话</span>
                    </div>
                    <div class="nav-item" title="模拟">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z M12 4c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3z"/>
                        </svg>
                        <span class="nav-item-text">模拟</span>
                    </div>
                </div>
                <div class="sidebar-footer nav-group">
                    <div class="nav-item" title="教程">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                        </svg>
                        <span class="nav-item-text">教程</span>
                    </div>
                    <div class="nav-item" title="邀请">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                        <span class="nav-item-text">邀请</span>
                    </div>
                    <div class="nav-item" title="夜间模式" onclick="toggleNightMode()">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9.37 5.51C9.19 6.15 9.1 6.82 9.1 7.5c0 4.08 3.32 7.4 7.4 7.4.68 0 1.35-.09 1.99-.27C17.45 17.19 14.93 19 12 19c-3.86 0-7-3.14-7-7 0-2.93 1.81-5.45 4.37-6.49z"/>
                        </svg>
                        <span class="nav-item-text">夜间</span>
                    </div>
                </div>
            </div>
        </nav>
    </div>

    <div class="app-container">
        <!-- 左侧面板 - 章节管理 -->
        <aside id="left-panel-resizable" class="panel left-panel">
            <div class="panel-header management-header">
                <span>章节管理</span>
                <div class="btn-group">
                    <button class="btn-new" title="切换排序">⇅</button>
                    <button class="btn-new" onclick="addNewChapter()">+ 新建</button>
                </div>
            </div>
            <div class="panel-content">
                <ul class="item-list" id="chapterList">
                    <li class="chapter-item active" data-chapter-id="1" data-create-time="1000000001" draggable="true">
                        <div class="chapter-title-wrapper">
                            <span class="chapter-number" onclick="editChapterTitle(event, '1')">第1章</span>
                            <span class="chapter-title">开端</span>
                        </div>
                        <div class="chapter-actions">
                            <button class="chapter-action-btn" onclick="deleteChapter(event, '1')" title="删除">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                </svg>
                            </button>
                        </div>
                    </li>
                    <li class="chapter-item" data-chapter-id="2" data-create-time="1000000002" draggable="true">
                        <div class="chapter-title-wrapper">
                            <span class="chapter-number" onclick="editChapterTitle(event, '2')">第2章</span>
                            <span class="chapter-title">发展</span>
                        </div>
                        <div class="chapter-actions">
                            <button class="chapter-action-btn" onclick="deleteChapter(event, '2')" title="删除">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                </svg>
                            </button>
                        </div>
                    </li>
                    <li class="chapter-item" data-chapter-id="3" data-create-time="1000000003" draggable="true">
                        <div class="chapter-title-wrapper">
                            <span class="chapter-number" onclick="editChapterTitle(event, '3')">第3章</span>
                            <span class="chapter-title">高潮</span>
                        </div>
                        <div class="chapter-actions">
                            <button class="chapter-action-btn" onclick="deleteChapter(event, '3')" title="删除">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                </svg>
                            </button>
                        </div>
                    </li>
                    <li class="chapter-item" data-chapter-id="4" data-create-time="1000000004" draggable="true">
                        <div class="chapter-title-wrapper">
                            <span class="chapter-number" onclick="editChapterTitle(event, '4')">第4章</span>
                            <span class="chapter-title">转折</span>
                        </div>
                        <div class="chapter-actions">
                            <button class="chapter-action-btn" onclick="deleteChapter(event, '4')" title="删除">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                </svg>
                            </button>
                        </div>
                    </li>
                    <li class="chapter-item" data-chapter-id="5" data-create-time="1000000005" draggable="true">
                        <div class="chapter-title-wrapper">
                            <span class="chapter-number" onclick="editChapterTitle(event, '5')">第5章</span>
                            <span class="chapter-title">结局</span>
                        </div>
                        <div class="chapter-actions">
                            <button class="chapter-action-btn" onclick="deleteChapter(event, '5')" title="删除">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                </svg>
                            </button>
                        </div>
                    </li>
                </ul>
            </div>
        </aside>

        <!-- 中间面板 -->
        <div id="center-panel-resizable" class="center-panel">
            <!-- 主工具栏 -->
            <div class="toolbar">
                <div class="btn-group">
                    <button onclick="undoAction()">撤回</button>
                    <button onclick="redoAction()">重做</button>
                    <button onclick="copyText()">复制</button>
                    <button onclick="openSearchDialog()">搜索</button>
                    <button onclick="openReplaceDialog()">查找替换</button>
                    <button onclick="smartFormat()">智能排版</button>
                    <button onclick="openFontSizeDialog()">字号调节</button>
                    <button onclick="openFontFamilyDialog()">字体更换</button>
                </div>
                <div class="theme-selector">
                    <button class="btn-back-shelf" onclick="window.location.href='书架.html'">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                        </svg>
                        返回书架
                    </button>
                    <button class="mode-switch-btn" onclick="toggleMode()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
                        </svg>
                        <span id="modeSwitchText">切换到脑洞创意</span>
                    </button>
                    <button title="显示/隐藏输入输出框" id="toggle-io-btn">显示编辑区</button>
                    <button data-theme="default" title="默认主题">🔵</button>
                    <button data-theme="green-leaf" title="豆沙绿">🟢</button>
                    <button data-theme="sepia" title="羊皮纸">🟠</button>
                    <button data-theme="dark" title="暗夜模式">🌙</button>
                </div>
            </div>

            <div class="center-content-wrapper">
                <!-- 底层: 本章内容 -->
                <div id="chapter-content-wrapper" class="text-area-wrapper">
                    <div class="panel-header content-header">
                        <span class="current-chapter-title" id="currentChapterTitle" onclick="editCurrentChapterTitle()">第1章 开端</span>
                        <span class="auto-save-status">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"/>
                            </svg>
                            自动保存中...
                        </span>
                    </div>
                    <textarea placeholder="这里显示当前所选章节的完整内容..." id="chapterContent"></textarea>
                </div>
                
                <!-- 编辑区容器（包含输入输出框和继续追问） -->
                <div id="edit-area-container" class="edit-area-container">
                    <!-- 悬浮层: 输入输出 -->
                    <div class="floating-input-output-container" id="floating-io-container">
                        <div id="input-wrapper" class="text-area-wrapper">
                            <div class="panel-header">输入框</div>
                            <textarea placeholder="此处为按照(提示词1、提示词2、提示词n)等步骤每一步需要输入或粘贴进来的内容... (不要清除用户的内容, 除非用户自己清空)" id="inputTextarea"></textarea>
                        </div>



                        <div id="output-wrapper" class="text-area-wrapper">
                            <div class="panel-header">生成框</div>
                            <textarea placeholder="此处为按照(提示词1、提示词2、提示词n)等步骤每一步输出的内容... (不要清除用户的内容, 除非用户自己清空)" id="outputTextarea" readonly></textarea>
                        </div>
                    </div>



                    <!-- 继续追问弹窗 -->
                    <div id="follow-up-modal" class="follow-up-modal">
                        <div class="follow-up-header">
                            <span class="follow-up-title">继续追问</span>
                            <button class="follow-up-close" onclick="closeFollowUp()">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                </svg>
                            </button>
                        </div>
                        <div class="follow-up-content">
                            <textarea class="follow-up-input" id="followUpInput" placeholder="请输入您的追问内容..."></textarea>
                        </div>
                        <div class="follow-up-footer">
                            <button class="follow-up-btn cancel" onclick="closeFollowUp()">取消</button>
                            <button class="follow-up-btn primary" onclick="submitFollowUp()">提交追问</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部按钮区 -->
            <div class="action-buttons">
                <button class="btn-secondary" onclick="openFollowUp()">继续追问</button>
                <button class="btn-secondary" onclick="openHistoryDialog()">历史记录</button>
                <button class="btn-secondary" onclick="testFunction()" style="background: red; color: white;">测试按钮</button>
            </div>
        </div>

        <!-- 右侧面板 -->
        <aside id="right-panel-resizable" class="panel right-panel">
            <div class="panel-header">控制面板</div>
            <div class="panel-content">
                <!-- 便签管理区域 -->
                <div class="notes-section">
                    <div class="notes-header">
                        <h3>便签管理</h3>
                        <button class="add-note-btn">+ 新建</button>
                    </div>
                    <ul class="item-list">
                        <li>重要设定笔记</li>
                        <li>灵感记录</li>
                        <li>情节大纲</li>
                        <li>人物关系图</li>
                    </ul>
                </div>

                <!-- 内容创作模块 (默认显示) -->
                <div id="writing-section">
                    <h3 class="content-section-header">写作功能区</h3>
                    <div class="control-group">
                        <label>选择功能</label>
                        <select id="writingFunction">
                            <option value="global">全局写作</option>
                            <option value="expand">扩写</option>
                            <option value="polish">润色</option>
                            <option value="continue">续写</option>
                            <option value="teardown">拆书</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>选择提示词</label>
                        <div class="prompt-selector-wrapper">
                            <select id="writingPromptSelect">
                                <option value="">请选择提示词...</option>
                            </select>
                            <button class="prompt-manage-btn" onclick="openPromptManager('writing')">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                                </svg>
                                管理提示词
                            </button>
                        </div>
                    </div>
                    <div class="control-group">
                        <label>模型选择</label>
                        <select>
                            <option>细腻贴合</option>
                            <option>逻辑精准</option>
                            <option>灵活创意</option>
                            <option>稳定版</option>
                            <option>综合版</option>
                            <option>测试版</option>
                        </select>
                    </div>
                </div>

                <!-- 灵感脑洞模块 (默认隐藏) -->
                <div id="inspiration-section">
                    <h3 class="content-section-header">脑洞创意区</h3>
                    <div class="control-group">
                        <label>创意类型</label>
                        <select id="inspirationType">
                            <option value="outline">大纲</option>
                            <option value="intro">导语</option>
                            <option value="character">人设</option>
                            <option value="worldview">世界观</option>
                            <option value="golden">黄金一章</option>
                            <option value="title">书名生成</option>
                            <option value="bookintro">简介撰写</option>
                            <option value="brainstorm">灵感创意</option>
                            <option value="script">剧本改写</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>选择提示词</label>
                        <div class="prompt-selector-wrapper">
                            <select id="inspirationPromptSelect">
                                <option value="">请选择提示词...</option>
                            </select>
                            <button class="prompt-manage-btn" onclick="openPromptManager('brainstorm')">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                                </svg>
                                管理提示词
                            </button>
                        </div>
                    </div>
                    <div class="control-group">
                        <label>模型选择</label>
                        <select>
                            <option>灵活创意</option>
                            <option>细腻贴合</option>
                            <option>逻辑精准</option>
                            <option>稳定版</option>
                            <option>综合版</option>
                            <option>测试版</option>
                        </select>
                    </div>
                </div>
                
                <!-- 通用附件模块 -->
                <h3 class="content-section-header">关联附件</h3>
                <div class="control-group">
                    <label for="char-card">角色卡 (可输入、可选择)</label>
                    <div style="display: flex; gap: 8px;">
                        <input type="text" id="char-card" placeholder="输入或选择角色..." style="flex: 1;">
                        <button class="btn-secondary" onclick="openCharacterCardDialog()">管理</button>
                    </div>
                </div>
                <div class="control-group">
                    <label for="knowledge-card">知识卡 (可输入、可选择)</label>
                    <div style="display: flex; gap: 8px;">
                        <input type="text" id="knowledge-card" placeholder="输入或选择知识..." style="flex: 1;">
                        <button class="btn-secondary" onclick="openKnowledgeCardDialog()">管理</button>
                    </div>
                </div>
                
                <div class="control-group">
                    <label for="chapter-select">章节 (可多选，最多10章)</label>
                    <div class="multi-select-container">
                        <div class="multi-select-display" id="chapter-select-display" onclick="toggleChapterDropdown()">
                            <span class="multi-select-placeholder">选择关联章节...</span>
                            <span class="multi-select-arrow">▼</span>
                        </div>
                        <div class="multi-select-dropdown" id="chapter-dropdown">
                            <!-- 动态生成章节选项 -->
                        </div>
                    </div>
                </div>

                <div class="control-group">
                    <label for="memo-select">便签 (可多选，最多10个)</label>
                    <div class="multi-select-container">
                        <div class="multi-select-display" id="memo-select-display" onclick="toggleMemoDropdown()">
                            <span class="multi-select-placeholder">选择关联便签...</span>
                            <span class="multi-select-arrow">▼</span>
                        </div>
                        <div class="multi-select-dropdown" id="memo-dropdown">
                            <div class="multi-select-option" data-value="note1">
                                <input type="checkbox" id="memo-note1" value="note1">
                                <label for="memo-note1">重要设定笔记</label>
                            </div>
                            <div class="multi-select-option" data-value="note2">
                                <input type="checkbox" id="memo-note2" value="note2">
                                <label for="memo-note2">灵感记录</label>
                            </div>
                            <div class="multi-select-option" data-value="note3">
                                <input type="checkbox" id="memo-note3" value="note3">
                                <label for="memo-note3">情节大纲</label>
                            </div>
                            <div class="multi-select-option" data-value="note4">
                                <input type="checkbox" id="memo-note4" value="note4">
                                <label for="memo-note4">人物关系图</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="generate-button-wrapper">
                <button class="generate-button">✨ 确认生成</button>
            </div>
        </aside>
    </div>

    <!-- 通知弹窗 -->
    <div class="notification" id="notification">
        <div class="notification-content">
            <div class="notification-icon">
                <!-- 图标会根据类型动态更新 -->
            </div>
            <span class="notification-text"></span>
        </div>
    </div>

    <!-- 提示词管理弹窗 - 完整复制专业版(3) -->
    <div class="modal" id="promptModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">提示词管理</h2>
                <button class="modal-close" onclick="closePromptManager()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <!-- 标签切换 -->
                <div class="modal-tabs">
                    <div class="modal-tab active" onclick="switchModalTab('my-prompts')">我的提示词</div>
                    <div class="modal-tab" onclick="switchModalTab('prompt-market')">提示词市场</div>
                    <div class="modal-tab" onclick="switchModalTab('create-prompt')">创建自定义提示词</div>
                </div>

                <!-- 我的提示词 -->
                <div class="modal-section active" id="my-prompts">
                    <div class="prompt-list" id="myPromptList">
                        <!-- 动态生成 -->
                    </div>
                </div>

                <!-- 提示词市场 -->
                <div class="modal-section" id="prompt-market">
                    <!-- 面包屑导航 -->
                    <div class="menu-breadcrumb" id="marketBreadcrumb">
                        <span class="breadcrumb-item" onclick="navigateToMarketLevel('type')">提示词市场</span>
                    </div>

                    <!-- 类型选择（一级菜单） -->
                    <div class="menu-level active" id="market-type-level">
                        <div class="menu-grid">
                            <div class="menu-item" onclick="navigateToMarketLevel('length', 'writing', '写作功能')">
                                <div class="menu-item-icon">✍️</div>
                                <div class="menu-item-name">写作功能</div>
                            </div>
                            <div class="menu-item" onclick="navigateToMarketLevel('length', 'brainstorm', '脑洞创意')">
                                <div class="menu-item-icon">💡</div>
                                <div class="menu-item-name">脑洞创意</div>
                            </div>
                        </div>
                    </div>

                    <!-- 长度选择（二级菜单） -->
                    <div class="menu-level" id="market-length-level">
                        <div class="menu-grid" id="lengthMenuGrid">
                            <!-- 动态生成 -->
                        </div>
                    </div>

                    <!-- 功能选择（三级菜单） -->
                    <div class="menu-level" id="market-function-level">
                        <div class="menu-grid" id="functionMenuGrid">
                            <!-- 动态生成 -->
                        </div>
                    </div>

                    <!-- 提示词列表（四级） -->
                    <div class="menu-level" id="market-prompts-level">
                        <div class="search-container">
                            <input type="text" class="search-input" placeholder="搜索提示词名称或作者..." id="marketSearchInput">
                        </div>
                        <div class="prompt-cards-grid" id="marketPromptsGrid">
                            <!-- 动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 创建自定义提示词 -->
                <div class="modal-section" id="create-prompt">
                    <form class="create-prompt-form" onsubmit="createCustomPrompt(event)">
                        <div class="form-group">
                            <label class="form-label">提示词分类</label>
                            <div class="cascader-group">
                                <select class="cascader-select" id="createType" onchange="updateCreateCascader('type')">
                                    <option value="">选择类型...</option>
                                    <option value="writing">写作功能</option>
                                    <option value="brainstorm">脑洞创意</option>
                                </select>
                                <select class="cascader-select" id="createLength" onchange="updateCreateCascader('length')" disabled>
                                    <option value="">选择长度...</option>
                                </select>
                                <select class="cascader-select" id="createFunction" disabled>
                                    <option value="">选择功能...</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">提示词名称</label>
                            <input type="text" class="form-input" id="promptName" placeholder="例如：情感细腻描写" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">提示词介绍</label>
                            <textarea class="form-textarea" id="promptIntro" placeholder="请输入提示词的介绍，说明其功能和特点..." required></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">提示词内容</label>
                            <textarea class="form-textarea" id="promptContent" placeholder="请输入提示词的具体内容..." required></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">创建提示词</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 提示词详情弹窗 -->
    <div class="prompt-detail-modal" id="promptDetailModal">
        <div class="prompt-detail-content">
            <div class="prompt-detail-header">
                <h3 class="prompt-detail-title" id="detailTitle"></h3>
                <div class="prompt-detail-meta">
                    <span id="detailAuthor"></span>
                    <span id="detailCategory"></span>
                </div>
            </div>
            <div class="prompt-detail-body">
                <div class="prompt-detail-section">
                    <label class="prompt-detail-label">提示词介绍</label>
                    <div class="prompt-detail-text" id="detailIntro"></div>
                </div>
                <div class="prompt-detail-section">
                    <label class="prompt-detail-label">使用说明</label>
                    <div class="prompt-detail-text" id="detailInstructions">
                        此提示词适用于小说创作中的相关场景，可以帮助您快速生成高质量的内容。使用时请根据实际需求调整参数。
                    </div>
                </div>
            </div>
            <div class="prompt-detail-footer">
                <button class="prompt-detail-btn cancel" onclick="closePromptDetail()">取消</button>
                <button class="prompt-detail-btn confirm" onclick="confirmAddPrompt()">确认添加</button>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/split.js/dist/split.min.js"></script>

    <script>
    // 使用立即执行函数避免全局污染
    (function() {
        'use strict';

        // 全局状态
        let currentMode = 'content'; // content or brainstorm
        let workTitle = '我的小说'; // 作品标题
        let draggedItem = null; // 拖动的章节
        let isEditAreaShown = false; // 编辑区显示状态
        
        // 等待DOM加载完成
        document.addEventListener('DOMContentLoaded', () => {
            // --- 初始化主题 ---
            const savedTheme = localStorage.getItem('theme') || 'default';
            document.documentElement.setAttribute('data-theme', savedTheme);

            // --- 初始化作品标题 ---
            const savedWorkTitle = localStorage.getItem('workTitle') || '我的小说';
            workTitle = savedWorkTitle;

            // --- 导航栏自动隐藏/显示逻辑 ---
            const sidebarWrapper = document.getElementById('sidebarWrapper');
            const sidebarTrigger = document.getElementById('sidebarTrigger');
            let sidebarTimer;
            
            // 鼠标进入感应区域
            sidebarTrigger.addEventListener('mouseenter', () => {
                clearTimeout(sidebarTimer);
                sidebarWrapper.classList.remove('collapsed');
            });
            
            // 鼠标离开感应区域
            sidebarTrigger.addEventListener('mouseleave', (e) => {
                // 检查鼠标是否移到了导航栏上
                const toElement = e.relatedTarget;
                if (toElement && !sidebarWrapper.contains(toElement)) {
                    sidebarTimer = setTimeout(() => {
                        sidebarWrapper.classList.add('collapsed');
                    }, 300);
                }
            });
            
            // 鼠标在导航栏区域内移动时保持显示
            sidebarWrapper.addEventListener('mouseenter', () => {
                clearTimeout(sidebarTimer);
            });
            
            // 鼠标离开整个导航栏区域
            sidebarWrapper.addEventListener('mouseleave', () => {
                sidebarTimer = setTimeout(() => {
                    sidebarWrapper.classList.add('collapsed');
                }, 300);
            });

            // --- 模式初始化 ---
            const urlParams = new URLSearchParams(window.location.search);
            const mode = urlParams.get('mode') || 'content';
            currentMode = mode;
            document.body.classList.add(`mode-${mode}`);
            updateModeSwitchButton(mode);

            // --- 布局初始化 (Split.js) ---
            Split(['#left-panel-resizable', '#center-panel-resizable', '#right-panel-resizable'], {
                sizes: [10, 70, 20],
                minSize: [150, 400, 280],
                gutterSize: 0,
                cursor: 'col-resize'
            });

            // 初始化自定义拖拽调整功能
            initResizeHandles();

            // 初始化提示词管理
            loadMyPrompts();

            // 点击遮罩关闭弹窗
            document.getElementById('promptModal').addEventListener('click', (e) => {
                if (e.target === e.currentTarget) {
                    closePromptManager();
                }
            });

            // 提示词详情弹窗
            document.getElementById('promptDetailModal').addEventListener('click', (e) => {
                if (e.target === e.currentTarget) {
                    closePromptDetail();
                }
            });

            // 搜索功能
            const searchInput = document.getElementById('marketSearchInput');
            if (searchInput) {
                searchInput.addEventListener('input', (e) => {
                    const searchTerm = e.target.value.toLowerCase();
                    filterMarketPrompts(searchTerm);
                });
            }

            // --- 输入输出框显示/隐藏 ---
            const toggleIOBtn = document.getElementById('toggle-io-btn');
            toggleIOBtn.addEventListener('click', toggleEditArea);

            // --- 皮肤切换功能 ---
            const themeButtons = document.querySelectorAll('.theme-selector button[data-theme]');
            themeButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const theme = button.dataset.theme;
                    document.documentElement.setAttribute('data-theme', theme);
                    localStorage.setItem('theme', theme);
                });
            });

            // --- 章节排序功能 ---
            const sortBtn = document.querySelector('button[title="切换排序"]');
            const chapterList = document.querySelector('#chapterList');
            let sortAscending = true;

            sortBtn.addEventListener('click', () => {
                const items = Array.from(chapterList.children);
                items.sort((a, b) => {
                    const aText = a.querySelector('.chapter-title').textContent;
                    const bText = b.querySelector('.chapter-title').textContent;
                    return sortAscending ? 
                        aText.localeCompare(bText, 'zh-CN') : 
                        bText.localeCompare(aText, 'zh-CN');
                });
                
                chapterList.innerHTML = '';
                items.forEach(item => chapterList.appendChild(item));
                
                sortAscending = !sortAscending;
            });

            // --- 章节拖动排序初始化 ---
            initChapterDragAndDrop();

            // --- 章节列表交互 ---
            bindChapterEvents();
            
            // --- 便签列表交互 ---
            const noteItems = document.querySelectorAll('.notes-section .item-list li');
            noteItems.forEach(item => {
                item.addEventListener('click', () => {
                    console.log('选中便签:', item.textContent);
                });
            });
            
            // --- 文本内容变化监听 ---
            const chapterContent = document.getElementById('chapterContent');
            let saveTimer;
            
            chapterContent.addEventListener('input', () => {
                clearTimeout(saveTimer);
                saveTimer = setTimeout(() => {
                    showAutoSave();
                }, 1000);
            });
        });

        // --- 全局函数定义 ---
        
        // 夜间模式切换
        window.toggleNightMode = function() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'default' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        };

        // 模式切换
        window.toggleMode = function() {
            const newMode = currentMode === 'content' ? 'brainstorm' : 'content';
            currentMode = newMode;
            
            document.body.classList.remove('mode-content', 'mode-brainstorm');
            document.body.classList.add(`mode-${newMode}`);
            
            updateModeSwitchButton(newMode);
            
            const url = new URL(window.location);
            url.searchParams.set('mode', newMode);
            window.history.replaceState({}, '', url);
            
            showModeTransition(newMode);
        };
        
        function updateModeSwitchButton(mode) {
            const btn = document.getElementById('modeSwitchText');
            btn.textContent = mode === 'content' ? '切换到脑洞创意' : '切换到写作功能';
        }
        
        function showModeTransition(mode) {
            const rightPanel = document.querySelector('.right-panel .panel-content');
            rightPanel.style.opacity = '0';
            rightPanel.style.transform = 'translateY(10px)';
            
            setTimeout(() => {
                rightPanel.style.transition = 'all 0.3s ease';
                rightPanel.style.opacity = '1';
                rightPanel.style.transform = 'translateY(0)';
            }, 100);
        }
        
        // 切换编辑区显示 - 修复问题6：追问框跟随隐藏但不跟随显示
        function toggleEditArea() {
            const editAreaContainer = document.getElementById('edit-area-container');
            const followUpModal = document.getElementById('follow-up-modal');
            const toggleBtn = document.getElementById('toggle-io-btn');

            isEditAreaShown = !isEditAreaShown;

            if (isEditAreaShown) {
                editAreaContainer.classList.add('show');
                toggleBtn.textContent = '隐藏编辑区';
                // 编辑区显示时，恢复追问框之前的状态
                if (isFollowUpShown) {
                    followUpModal.classList.remove('hidden');
                }
            } else {
                editAreaContainer.classList.remove('show');
                toggleBtn.textContent = '显示编辑区';
                // 编辑区隐藏时，追问框也跟随隐藏（但不改变isFollowUpShown状态）
                followUpModal.classList.add('hidden');
            }
        }
        
        // 编辑当前章节标题
        window.editCurrentChapterTitle = function() {
            const titleSpan = document.getElementById('currentChapterTitle');
            const currentTitle = titleSpan.textContent;
            
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'current-chapter-title-input';
            input.value = currentTitle;
            
            titleSpan.replaceWith(input);
            input.focus();
            input.select();
            
            function saveTitle() {
                const newTitle = input.value.trim() || currentTitle;
                const newSpan = document.createElement('span');
                newSpan.className = 'current-chapter-title';
                newSpan.id = 'currentChapterTitle';
                newSpan.textContent = newTitle;
                newSpan.onclick = editCurrentChapterTitle;
                input.replaceWith(newSpan);
                
                // 同步更新章节列表中的标题
                const activeChapter = document.querySelector('.chapter-item.active');
                if (activeChapter) {
                    const parts = newTitle.match(/^(第\d+章)\s*(.*)$/);
                    if (parts) {
                        activeChapter.querySelector('.chapter-number').textContent = parts[1];
                        activeChapter.querySelector('.chapter-title').textContent = parts[2] || '';
                    }
                }
                
                updateChapterSelects();
                showAutoSave();
            }
            
            input.addEventListener('blur', saveTitle);
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    saveTitle();
                } else if (e.key === 'Escape') {
                    const span = document.createElement('span');
                    span.className = 'current-chapter-title';
                    span.id = 'currentChapterTitle';
                    span.textContent = currentTitle;
                    span.onclick = editCurrentChapterTitle;
                    input.replaceWith(span);
                }
            });
        };
        
        // 章节标题编辑功能 - 修改为只编辑"第N章"部分
        window.editChapterTitle = function(event, chapterId) {
            event.stopPropagation();
            const chapterItem = document.querySelector(`[data-chapter-id="${chapterId}"]`);
            const numberSpan = chapterItem.querySelector('.chapter-number');
            const titleSpan = chapterItem.querySelector('.chapter-title');
            const currentNumber = numberSpan.textContent;
            const currentTitle = titleSpan.textContent;
            
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'chapter-number-input';
            input.value = currentNumber + ' ' + currentTitle;
            
            numberSpan.style.display = 'none';
            titleSpan.style.display = 'none';
            numberSpan.parentNode.insertBefore(input, numberSpan);
            input.focus();
            input.select();
            
            function saveTitle() {
                const newValue = input.value.trim() || (currentNumber + ' ' + currentTitle);
                const parts = newValue.match(/^(第\d+章)\s*(.*)$/);

                if (parts) {
                    numberSpan.textContent = parts[1];
                    titleSpan.textContent = parts[2] || currentTitle;
                } else {
                    // 如果格式不正确，保持原样
                    numberSpan.textContent = currentNumber;
                    titleSpan.textContent = newValue || currentTitle;
                }

                numberSpan.style.display = '';
                titleSpan.style.display = '';
                input.remove();

                // 修复问题2：立即更新当前章节标题显示
                if (chapterItem.classList.contains('active')) {
                    const newDisplayTitle = numberSpan.textContent + ' ' + titleSpan.textContent;
                    document.getElementById('currentChapterTitle').textContent = newDisplayTitle;
                }

                updateChapterSelects();
                showAutoSave();
                showNotification('章节标题已更新', 'success');
            }
            
            input.addEventListener('blur', saveTitle);
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    saveTitle();
                } else if (e.key === 'Escape') {
                    numberSpan.style.display = '';
                    titleSpan.style.display = '';
                    input.remove();
                }
            });
        };
        
        // 章节删除功能
        window.deleteChapter = function(event, chapterId) {
            event.stopPropagation();
            showConfirmDialog('确定要删除这个章节吗？', () => {
                const chapterItem = document.querySelector(`[data-chapter-id="${chapterId}"]`);
                
                chapterItem.style.transition = 'all 0.3s ease';
                chapterItem.style.opacity = '0';
                chapterItem.style.transform = 'translateX(-20px)';
                
                setTimeout(() => {
                    chapterItem.remove();
                    
                    if (chapterItem.classList.contains('active')) {
                        const firstChapter = document.querySelector('.chapter-item');
                        if (firstChapter) {
                            firstChapter.classList.add('active');
                            updateChapterContent(firstChapter.dataset.chapterId);
                        }
                    }
                    
                    updateChapterSelects();
                    showNotification('章节已删除', 'success');
                }, 300);
            });
        };
        
        // 确认对话框
        function showConfirmDialog(message, onConfirm) {
            const confirmModal = document.createElement('div');
            confirmModal.className = 'prompt-detail-modal';
            confirmModal.innerHTML = `
                <div class="prompt-detail-content" style="max-width: 400px;">
                    <div class="prompt-detail-body" style="padding: 30px; text-align: center;">
                        <div style="font-size: 18px; font-weight: 600; color: var(--text-dark); margin-bottom: 20px;">
                            ${message}
                        </div>
                    </div>
                    <div class="prompt-detail-footer">
                        <button class="prompt-detail-btn cancel" onclick="this.closest('.prompt-detail-modal').remove()">取消</button>
                        <button class="prompt-detail-btn confirm" id="confirmDeleteBtn">确认</button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(confirmModal);
            confirmModal.style.display = 'flex';
            
            requestAnimationFrame(() => {
                confirmModal.classList.add('show');
            });
            
            confirmModal.querySelector('#confirmDeleteBtn').addEventListener('click', () => {
                onConfirm();
                confirmModal.classList.remove('show');
                setTimeout(() => confirmModal.remove(), 300);
            });
            
            confirmModal.addEventListener('click', (e) => {
                if (e.target === confirmModal) {
                    confirmModal.classList.remove('show');
                    setTimeout(() => confirmModal.remove(), 300);
                }
            });
        }
        
        // 新建章节功能 - 修复问题1：确保新建章节的标题编辑功能立即可用
        window.addNewChapter = function() {
            const chapterList = document.getElementById('chapterList');
            const chapterCount = chapterList.children.length;
            const newChapterId = Date.now().toString();

            const newChapter = document.createElement('li');
            newChapter.className = 'chapter-item';
            newChapter.dataset.chapterId = newChapterId;
            newChapter.dataset.createTime = Date.now(); // 添加创建时间
            newChapter.draggable = true;
            newChapter.innerHTML = `
                <div class="chapter-title-wrapper">
                    <span class="chapter-number" onclick="editChapterTitle(event, '${newChapterId}')">第${chapterCount + 1}章</span>
                    <span class="chapter-title">新章节</span>
                </div>
                <div class="chapter-actions">
                    <button class="chapter-action-btn" onclick="deleteChapter(event, '${newChapterId}')" title="删除">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                        </svg>
                    </button>
                </div>
            `;

            chapterList.appendChild(newChapter);

            // 重新绑定事件
            bindChapterEvents();
            initChapterDragAndDrop();

            // 设置为当前活动章节
            document.querySelectorAll('.chapter-item').forEach(item => item.classList.remove('active'));
            newChapter.classList.add('active');
            updateChapterContent(newChapterId);

            updateChapterSelects();

            // 提示用户可以点击章节编号或在编辑器标题栏修改章节标题
            showNotification('新章节已创建，点击章节编号或编辑器标题栏可修改标题', 'info');
        };
        
        // 章节拖动排序 - 修复问题3：重新实现拖拽功能
        function initChapterDragAndDrop() {
            const chapterItems = document.querySelectorAll('.chapter-item');

            chapterItems.forEach(item => {
                // 确保元素可拖拽
                item.draggable = true;

                // 清除所有旧的事件监听器
                const newItem = item.cloneNode(true);
                item.parentNode.replaceChild(newItem, item);

                // 重新绑定点击事件
                newItem.addEventListener('click', function(e) {
                    if (e.target.closest('.chapter-actions') ||
                        e.target.classList.contains('chapter-number') ||
                        e.target.classList.contains('chapter-number-input')) {
                        return;
                    }

                    document.querySelectorAll('.chapter-item').forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                    updateChapterContent(this.dataset.chapterId);
                });

                // 添加拖拽事件监听器
                newItem.addEventListener('dragstart', handleDragStart);
                newItem.addEventListener('dragover', handleDragOver);
                newItem.addEventListener('drop', handleDrop);
                newItem.addEventListener('dragend', handleDragEnd);
                newItem.addEventListener('dragenter', handleDragEnter);
                newItem.addEventListener('dragleave', handleDragLeave);
            });
        }
        
        function handleDragStart(e) {
            draggedItem = this;
            this.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', this.outerHTML);
            e.dataTransfer.setData('text/plain', this.dataset.chapterId);

            // 创建自定义拖拽图像，避免残影
            const dragImage = document.createElement('div');
            dragImage.style.position = 'absolute';
            dragImage.style.top = '-1000px';
            dragImage.style.left = '-1000px';
            dragImage.style.width = '200px';
            dragImage.style.height = '40px';
            dragImage.style.background = 'var(--primary-color)';
            dragImage.style.color = 'var(--text-on-primary)';
            dragImage.style.borderRadius = '8px';
            dragImage.style.display = 'flex';
            dragImage.style.alignItems = 'center';
            dragImage.style.justifyContent = 'center';
            dragImage.style.fontSize = 'var(--font-size-sm)';
            dragImage.style.fontWeight = '500';
            dragImage.style.opacity = '0.9';
            dragImage.textContent = this.querySelector('.chapter-title-wrapper').textContent || '章节';

            document.body.appendChild(dragImage);
            e.dataTransfer.setDragImage(dragImage, 100, 20);

            // 立即移除拖拽图像
            setTimeout(() => {
                if (document.body.contains(dragImage)) {
                    document.body.removeChild(dragImage);
                }
            }, 0);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            if (this !== draggedItem) {
                const rect = this.getBoundingClientRect();
                const midY = rect.top + rect.height / 2;

                // 清除所有拖拽样式
                document.querySelectorAll('.chapter-item').forEach(item => {
                    item.classList.remove('drag-over-top', 'drag-over-bottom');
                });

                // 根据鼠标位置添加样式
                if (e.clientY < midY) {
                    this.classList.add('drag-over-top');
                } else {
                    this.classList.add('drag-over-bottom');
                }
            }

            return false;
        }

        function handleDragEnter(e) {
            e.preventDefault();
        }

        function handleDragLeave(e) {
            // 检查是否真的离开了元素
            const rect = this.getBoundingClientRect();
            const x = e.clientX;
            const y = e.clientY;

            if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
                this.classList.remove('drag-over-top', 'drag-over-bottom');
            }
        }

        function handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();

            // 清除所有拖拽样式
            document.querySelectorAll('.chapter-item').forEach(item => {
                item.classList.remove('drag-over-top', 'drag-over-bottom');
            });

            if (draggedItem && draggedItem !== this) {
                const list = this.parentNode;
                const rect = this.getBoundingClientRect();
                const midY = rect.top + rect.height / 2;
                const insertBefore = e.clientY < midY;

                if (insertBefore) {
                    list.insertBefore(draggedItem, this);
                } else {
                    list.insertBefore(draggedItem, this.nextSibling);
                }

                // 重新初始化拖拽功能
                initChapterDragAndDrop();
                updateChapterSelects();
                showAutoSave();
                showNotification('章节顺序已更新', 'success');
            }

            return false;
        }

        function handleDragEnd(e) {
            const items = document.querySelectorAll('.chapter-item');
            items.forEach(item => {
                item.classList.remove('dragging', 'drag-over-top', 'drag-over-bottom');
            });
            draggedItem = null;
        }
        
        // 继续追问功能 - 修复问题2：确保能正常弹出对话框
        let isFollowUpShown = true; // 默认显示追问框

        window.openFollowUp = function() {
            const editAreaContainer = document.getElementById('edit-area-container');
            const followUpModal = document.getElementById('follow-up-modal');
            const toggleBtn = document.getElementById('toggle-io-btn');

            // 显示整个编辑区
            if (!isEditAreaShown) {
                isEditAreaShown = true;
                editAreaContainer.classList.add('show');
                if (toggleBtn) {
                    toggleBtn.textContent = '隐藏编辑区';
                }
            }

            // 显示追问框
            if (followUpModal) {
                isFollowUpShown = true;
                followUpModal.classList.remove('hidden');
                followUpModal.style.display = 'flex';
            }

            // 聚焦输入框
            setTimeout(() => {
                const followUpInput = document.getElementById('followUpInput');
                if (followUpInput) {
                    followUpInput.focus();
                }
            }, 200);
        };

        window.closeFollowUp = function() {
            const followUpModal = document.getElementById('follow-up-modal');
            const followUpInput = document.getElementById('followUpInput');

            if (followUpModal) {
                isFollowUpShown = false;
                followUpModal.classList.add('hidden');
            }

            if (followUpInput) {
                followUpInput.value = '';
            }
        };
        
        window.submitFollowUp = function() {
            const input = document.getElementById('followUpInput');
            const content = input.value.trim();
            
            if (!content) {
                showNotification('请输入追问内容', 'error');
                return;
            }
            
            // 将追问内容添加到输入框
            const inputTextarea = document.getElementById('inputTextarea');
            if (inputTextarea.value) {
                inputTextarea.value += '\n\n--- 继续追问 ---\n' + content;
            } else {
                inputTextarea.value = content;
            }
            
            // 模拟AI生成（实际应用中这里应该调用API）
            const outputTextarea = document.getElementById('outputTextarea');
            outputTextarea.value += '\n\n[AI正在根据您的追问生成内容...]';
            
            // 清空追问输入框
            input.value = '';
            
            showNotification('追问已提交', 'success');
            
            // 模拟生成延迟
            setTimeout(() => {
                outputTextarea.value = outputTextarea.value.replace(
                    '[AI正在根据您的追问生成内容...]',
                    '根据您的追问，这里是生成的内容...'
                );
            }, 2000);
        };
        
        // 辅助函数 - 修复问题3：简化事件绑定，避免干扰拖拽
        function bindChapterEvents() {
            // 这个函数现在由initChapterDragAndDrop处理，避免重复绑定
            console.log('Chapter events bound via initChapterDragAndDrop');
        }
        
        function updateChapterSelects() {
            // 更新多选组件的章节下拉选项
            if (document.getElementById('chapter-dropdown')) {
                updateChapterDropdown();
            }

            // 更新提示词选择框（如果存在）
            updatePromptSelects();
        }
        
        function updateChapterContent(chapterId) {
            const chapterContent = document.getElementById('chapterContent');
            const chapter = document.querySelector(`[data-chapter-id="${chapterId}"]`);
            const chapterNumber = chapter.querySelector('.chapter-number').textContent;
            const chapterTitle = chapter.querySelector('.chapter-title').textContent;
            
            // 更新当前章节标题显示
            document.getElementById('currentChapterTitle').textContent = chapterNumber + ' ' + chapterTitle;
            
            chapterContent.style.opacity = '0';
            setTimeout(() => {
                chapterContent.value = `这是${chapterNumber} ${chapterTitle}的内容...`;
                chapterContent.style.transition = 'opacity 0.3s';
                chapterContent.style.opacity = '1';
            }, 100);
        }
        
        let autoSaveTimer;
        function showAutoSave() {
            const autoSaveStatus = document.querySelector('.auto-save-status');
            autoSaveStatus.style.opacity = '1';
            
            clearTimeout(autoSaveTimer);
            autoSaveTimer = setTimeout(() => {
                autoSaveStatus.style.transition = 'opacity 0.5s';
                autoSaveStatus.style.opacity = '0.5';
            }, 2000);
        }
        
        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const icon = notification.querySelector('.notification-icon');
            const text = notification.querySelector('.notification-text');
            
            // 设置图标
            const icons = {
                success: '<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/></svg>',
                error: '<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>',
                info: '<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>'
            };
            
            icon.innerHTML = icons[type] || icons.info;
            text.textContent = message;
            
            // 设置类型
            notification.className = `notification ${type}`;
            
            // 显示通知
            notification.classList.add('show');
            
            // 自动隐藏
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
        
        // 提示词管理功能 - 完整复制专业版(3)
        let currentMarketType = '';
        let currentMarketLength = '';
        let currentMarketFunction = '';
        let myPrompts = [];
        let pendingPrompt = null;
        let userName = '默认作者';

        // 提示词市场数据结构（添加intro字段）
        const marketData = {
            writing: {
                '长篇': {
                    '全局写作': [
                        {
                            id: 1,
                            title: '玄幻小说章节创作',
                            desc: '专注于玄幻题材的章节创作，包含修炼、战斗、奇遇等元素',
                            intro: '这是一个专门为玄幻小说设计的提示词，能够帮助您创作出充满想象力的修炼世界。它包含了完整的玄幻元素体系，如境界划分、功法设定、战斗描写等，适合创作长篇玄幻小说的各个章节。',
                            author: '作者A',
                            likes: 234,
                            uses: 567
                        },
                        {
                            id: 2,
                            title: '都市言情细腻描写',
                            desc: '擅长都市背景下的感情戏创作，注重心理描写',
                            intro: '专为都市言情小说打造的提示词，注重情感的细腻刻画和心理活动的深入描写。能够帮助您创作出打动人心的都市爱情故事，特别适合处理复杂的感情纠葛和人物内心世界。',
                            author: '作者B',
                            likes: 189,
                            uses: 432
                        }
                    ],
                    '扩写': [
                        {
                            id: 3,
                            title: '战斗场面扩写专家',
                            desc: '将简单的战斗描述扩展成精彩的打斗场面',
                            intro: '专门用于扩写战斗场景的提示词，能够将简短的动作描述扩展成视觉感强烈、节奏紧凑的战斗场面。包含招式描写、环境互动、心理变化等多个维度。',
                            author: '作者C',
                            likes: 156,
                            uses: 378
                        }
                    ],
                    '润色': [
                        {
                            id: 4,
                            title: '古风文笔润色',
                            desc: '为文字增添古典韵味，提升文学性',
                            intro: '将现代白话文润色成具有古典韵味的文字，适合历史、仙侠、古风等题材。能够在保持原意的基础上，增加诗词引用、典故运用，提升文章的文学性和艺术性。',
                            author: '作者D',
                            likes: 201,
                            uses: 489
                        }
                    ],
                    '续写': [
                        {
                            id: 5,
                            title: '剧情自然延续',
                            desc: '根据前文内容自然续写，保持风格统一',
                            intro: '智能分析前文的写作风格、人物性格、情节走向，生成与原文高度一致的续写内容。确保故事的连贯性和人物的一致性，避免突兀的转折。',
                            author: '作者E',
                            likes: 178,
                            uses: 423
                        }
                    ],
                    '拆书': [
                        {
                            id: 6,
                            title: '经典作品深度分析',
                            desc: '分析作品结构、技巧、亮点',
                            intro: '深入剖析经典作品的写作技巧，包括结构安排、人物塑造、情节设计、语言运用等方面。帮助写作者学习和借鉴大师的创作手法。',
                            author: '作者F',
                            likes: 145,
                            uses: 321
                        }
                    ]
                }
            },
            brainstorm: {
                '长篇': {
                    '大纲': [
                        {
                            id: 14,
                            title: '长篇整体架构设计',
                            desc: '构建20万字以上长篇的完整框架',
                            intro: '为长篇小说设计完整的故事架构，包括主线剧情、支线设计、人物成长曲线、冲突设置等。帮助您规划一个结构完整、节奏合理的长篇故事。',
                            author: '作者N',
                            likes: 234,
                            uses: 678
                        }
                    ],
                    '人设': [
                        {
                            id: 17,
                            title: '立体人物塑造',
                            desc: '创建有血有肉的角色形象',
                            intro: '从外貌、性格、背景、动机等多个维度塑造立体的人物形象。包含人物小传、性格分析、成长轨迹设计，让每个角色都有独特的魅力和记忆点。',
                            author: '作者Q',
                            likes: 256,
                            uses: 723
                        }
                    ],
                    '世界观': [
                        {
                            id: 23,
                            title: '宏大世界构建',
                            desc: '创建完整的故事世界体系',
                            intro: '构建一个逻辑自洽、细节丰富的虚构世界。包括地理环境、历史背景、社会制度、文化风俗、魔法/科技体系等，为您的故事提供坚实的世界观基础。',
                            author: '作者W',
                            likes: 245,
                            uses: 698
                        }
                    ]
                }
            }
        };

        // 菜单配置
        const menuConfig = {
            writing: {
                lengths: ['长篇', '中篇', '短篇', '剧本'],
                functions: {
                    '长篇': ['全局写作', '扩写', '润色', '续写', '拆书', '起名', '其他'],
                    '中篇': ['全局写作', '正文', '扩写', '润色', '续写', '拆书', '起名', '其他'],
                    '短篇': ['全局写作', '正文', '扩写', '润色', '续写', '拆书', '起名', '其他'],
                    '剧本': ['全局写作', '正文', '扩写', '润色', '续写', '拆书', '起名', '其他']
                }
            },
            brainstorm: {
                lengths: ['长篇', '中篇', '短篇', '剧本'],
                functions: {
                    '长篇': ['大纲', '卷纲', '章纲', '人设', '起名', '灵感创意', '黄金一章', '书名生成', '简介撰写', '世界观', '金手指', '其他'],
                    '中篇': ['导语', '大纲', '人设', '起名', '灵感创意', '加梗润色', '其他'],
                    '短篇': ['导语', '大纲', '人设', '起名', '灵感创意', '加梗润色', '其他'],
                    '剧本': ['改写', '原创', '其他']
                }
            }
        };

        window.openPromptManager = function(type) {
            currentMarketType = type;
            const modal = document.getElementById('promptModal');
            modal.style.display = 'flex';

            // 触发动画
            requestAnimationFrame(() => {
                modal.classList.add('show');
            });

            // 重置市场导航
            navigateToMarketLevel('type');
            loadMyPrompts();
        };

        window.closePromptManager = function() {
            const modal = document.getElementById('promptModal');
            modal.classList.remove('show');

            // 等待动画完成后隐藏
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        };

        // 切换模态框标签
        window.switchModalTab = function(tabId) {
            // 更新标签状态
            document.querySelectorAll('.modal-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 切换内容
            document.querySelectorAll('.modal-section').forEach(section => {
                section.classList.remove('active');
            });
            document.getElementById(tabId).classList.add('active');
        };

        // 提示词市场导航
        window.navigateToMarketLevel = function(level, value = '', label = '') {
            // 隐藏所有层级
            document.querySelectorAll('.menu-level').forEach(el => {
                el.classList.remove('active');
            });

            // 更新面包屑
            const breadcrumb = document.getElementById('marketBreadcrumb');

            // 处理面包屑导航，避免重复
            if (level === 'type') {
                breadcrumb.innerHTML = '<span class="breadcrumb-item" onclick="navigateToMarketLevel(\'type\')">提示词市场</span>';
                document.getElementById('market-type-level').classList.add('active');
            } else if (level === 'length') {
                currentMarketType = value;
                breadcrumb.innerHTML = `
                    <span class="breadcrumb-item" onclick="navigateToMarketLevel('type')">提示词市场</span>
                    <span class="breadcrumb-separator">></span>
                    <span class="breadcrumb-item" onclick="navigateToMarketLevel('length', '${value}', '${label}')">${label}</span>
                `;
                renderLengthMenu(value);
                document.getElementById('market-length-level').classList.add('active');
            } else if (level === 'function') {
                currentMarketLength = value;
                const typeLabel = currentMarketType === 'writing' ? '写作功能' : '脑洞创意';
                breadcrumb.innerHTML = `
                    <span class="breadcrumb-item" onclick="navigateToMarketLevel('type')">提示词市场</span>
                    <span class="breadcrumb-separator">></span>
                    <span class="breadcrumb-item" onclick="navigateToMarketLevel('length', '${currentMarketType}', '${typeLabel}')">${typeLabel}</span>
                    <span class="breadcrumb-separator">></span>
                    <span class="breadcrumb-item" onclick="navigateToMarketLevel('function', '${value}', '${label}')">${label}</span>
                `;
                renderFunctionMenu(currentMarketType, value);
                document.getElementById('market-function-level').classList.add('active');
            } else if (level === 'prompts') {
                currentMarketFunction = value;
                const typeLabel = currentMarketType === 'writing' ? '写作功能' : '脑洞创意';
                breadcrumb.innerHTML = `
                    <span class="breadcrumb-item" onclick="navigateToMarketLevel('type')">提示词市场</span>
                    <span class="breadcrumb-separator">></span>
                    <span class="breadcrumb-item" onclick="navigateToMarketLevel('length', '${currentMarketType}', '${typeLabel}')">${typeLabel}</span>
                    <span class="breadcrumb-separator">></span>
                    <span class="breadcrumb-item" onclick="navigateToMarketLevel('function', '${currentMarketLength}', '${currentMarketLength}')">${currentMarketLength}</span>
                    <span class="breadcrumb-separator">></span>
                    <span class="breadcrumb-item">${label}</span>
                `;
                renderPromptsList(currentMarketType, currentMarketLength, value);
                document.getElementById('market-prompts-level').classList.add('active');
            }
        };

        // 渲染长度菜单
        function renderLengthMenu(type) {
            const grid = document.getElementById('lengthMenuGrid');
            const lengths = menuConfig[type].lengths;

            grid.innerHTML = lengths.map(length => `
                <div class="menu-item" onclick="navigateToMarketLevel('function', '${length}', '${length}')">
                    <div class="menu-item-icon">${getIcon(length)}</div>
                    <div class="menu-item-name">${length}</div>
                </div>
            `).join('');
        }

        // 渲染功能菜单
        function renderFunctionMenu(type, length) {
            const grid = document.getElementById('functionMenuGrid');
            const functions = menuConfig[type].functions[length];

            grid.innerHTML = functions.map(func => `
                <div class="menu-item" onclick="navigateToMarketLevel('prompts', '${func}', '${func}')">
                    <div class="menu-item-icon">${getIcon(func)}</div>
                    <div class="menu-item-name">${func}</div>
                </div>
            `).join('');
        }

        // 渲染提示词列表
        function renderPromptsList(type, length, func) {
            const grid = document.getElementById('marketPromptsGrid');
            const prompts = marketData[type]?.[length]?.[func] || [];

            if (prompts.length === 0) {
                grid.innerHTML = '<div style="grid-column: 1/-1; text-align: center; color: var(--text-light);">暂无提示词</div>';
                return;
            }

            grid.innerHTML = prompts.map((prompt, index) => `
                <div class="prompt-card" style="--card-index: ${index}" data-prompt-id="${prompt.id}" onclick="showPromptDetail({
                    id: ${prompt.id},
                    title: '${prompt.title}',
                    desc: '${prompt.desc}',
                    intro: '${prompt.intro || prompt.desc}',
                    author: '${prompt.author}',
                    type: '${type}',
                    length: '${length}',
                    function: '${func}'
                })">
                    <div class="prompt-card-title">${prompt.title}</div>
                    <div class="prompt-card-desc">${prompt.desc}</div>
                    <div class="prompt-card-footer">
                        <div class="prompt-author">
                            <span>@${prompt.author}</span>
                        </div>
                        <div class="prompt-stats">
                            <div class="prompt-stat">
                                <span>❤️</span>
                                <span>${prompt.likes}</span>
                            </div>
                            <div class="prompt-stat">
                                <span>📊</span>
                                <span>${prompt.uses}</span>
                            </div>
                         </div>
                    </div>
                </div>
            `).join('');

            // 标记已添加的提示词
            myPrompts.forEach(myPrompt => {
                const card = document.querySelector(`[data-prompt-id="${myPrompt.id}"]`);
                if (card) {
                    card.classList.add('added');
                }
            });
        }

        // 搜索过滤
        function filterMarketPrompts(searchTerm) {
            const cards = document.querySelectorAll('#marketPromptsGrid .prompt-card');
            cards.forEach(card => {
                const title = card.querySelector('.prompt-card-title').textContent.toLowerCase();
                const author = card.querySelector('.prompt-author span').textContent.toLowerCase();

                if (title.includes(searchTerm) || author.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // 显示提示词详情
        function showPromptDetail(prompt) {
            pendingPrompt = prompt;

            document.getElementById('detailTitle').textContent = prompt.title || prompt.name;
            document.getElementById('detailAuthor').textContent = `作者：${prompt.author || userName}`;
            document.getElementById('detailCategory').textContent = `${prompt.type || currentMarketType} > ${prompt.length || currentMarketLength} > ${prompt.function || currentMarketFunction}`;
            document.getElementById('detailIntro').textContent = prompt.intro || prompt.desc || '暂无详细介绍';

            const modal = document.getElementById('promptDetailModal');
            modal.style.display = 'flex';

            requestAnimationFrame(() => {
                modal.classList.add('show');
            });
        }

        // 关闭提示词详情
        window.closePromptDetail = function() {
            const modal = document.getElementById('promptDetailModal');
            modal.classList.remove('show');

            setTimeout(() => {
                modal.style.display = 'none';
                pendingPrompt = null;
            }, 300);
        };

        // 确认添加提示词
        window.confirmAddPrompt = function() {
            if (!pendingPrompt) return;

            // 添加到我的提示词
            const myPrompt = {
                ...pendingPrompt,
                addTime: new Date().toISOString()
            };

            myPrompts.push(myPrompt);
            saveMyPrompts();
            loadMyPrompts();

            // 标记已添加
            const card = document.querySelector(`[data-prompt-id="${pendingPrompt.id}"]`);
            if (card) {
                card.classList.add('added');
            }

            closePromptDetail();
            showNotification('提示词添加成功！', 'success');
        };

        // 创建自定义提示词
        window.createCustomPrompt = function(event) {
            event.preventDefault();

            const type = document.getElementById('createType').value;
            const length = document.getElementById('createLength').value;
            const func = document.getElementById('createFunction').value;
            const name = document.getElementById('promptName').value;
            const intro = document.getElementById('promptIntro').value;
            const content = document.getElementById('promptContent').value;

            if (!type || !length || !func) {
                showNotification('请选择完整的提示词分类', 'error');
                return;
            }

            showConfirmDialog('确定要创建这个提示词吗？', () => {
                const newPrompt = {
                    id: Date.now(),
                    type,
                    length,
                    function: func,
                    name,
                    title: name,
                    intro,
                    content,
                    author: userName,
                    createTime: new Date().toISOString()
                };

                myPrompts.push(newPrompt);
                saveMyPrompts();
                loadMyPrompts();

                // 重置表单
                event.target.reset();
                document.getElementById('createLength').disabled = true;
                document.getElementById('createFunction').disabled = true;

                showNotification('提示词创建成功！', 'success');
            });
        };

        // 更新创建表单的级联选择
        window.updateCreateCascader = function(level) {
            if (level === 'type') {
                const type = document.getElementById('createType').value;
                const lengthSelect = document.getElementById('createLength');

                if (type) {
                    lengthSelect.disabled = false;
                    const lengths = menuConfig[type].lengths;
                    lengthSelect.innerHTML = '<option value="">选择长度...</option>' +
                        lengths.map(length => `<option value="${length}">${length}</option>`).join('');
                } else {
                    lengthSelect.disabled = true;
                    lengthSelect.innerHTML = '<option value="">选择长度...</option>';
                }

                // 重置下级
                document.getElementById('createFunction').disabled = true;
                document.getElementById('createFunction').innerHTML = '<option value="">选择功能...</option>';
            } else if (level === 'length') {
                const type = document.getElementById('createType').value;
                const length = document.getElementById('createLength').value;
                const functionSelect = document.getElementById('createFunction');

                if (type && length) {
                    functionSelect.disabled = false;
                    const functions = menuConfig[type].functions[length];
                    functionSelect.innerHTML = '<option value="">选择功能...</option>' +
                        functions.map(func => `<option value="${func}">${func}</option>`).join('');
                } else {
                    functionSelect.disabled = true;
                    functionSelect.innerHTML = '<option value="">选择功能...</option>';
                }
            }
        };

        // 删除提示词
        window.deletePrompt = function(promptId) {
            showConfirmDialog('确定要删除这个提示词吗？', () => {
                myPrompts = myPrompts.filter(p => p.id !== promptId);
                saveMyPrompts();
                loadMyPrompts();
                showNotification('提示词已删除', 'success');
            });
        };

        // 加载我的提示词
        function loadMyPrompts() {
            const savedPrompts = localStorage.getItem('myPrompts');
            if (savedPrompts) {
                myPrompts = JSON.parse(savedPrompts);
            }

            renderMyPrompts();
            updatePromptSelects();
        }

        // 保存我的提示词
        function saveMyPrompts() {
            localStorage.setItem('myPrompts', JSON.stringify(myPrompts));
        }

        // 渲染我的提示词
        function renderMyPrompts() {
            const list = document.getElementById('myPromptList');

            if (myPrompts.length === 0) {
                list.innerHTML = '<div style="text-align: center; color: var(--text-light); padding: 40px;">暂无提示词，请从市场添加或创建新的提示词</div>';
                return;
            }

            list.innerHTML = myPrompts.map((prompt, index) => `
                <div class="prompt-item" style="--item-index: ${index}">
                    <div class="prompt-info">
                        <div class="prompt-name">${prompt.name || prompt.title}</div>
                        <div class="prompt-desc">${prompt.type} > ${prompt.length} > ${prompt.function}</div>
                    </div>
                    <div class="prompt-actions">
                        <button class="prompt-action-btn delete" onclick="deletePrompt(${prompt.id})">删除</button>
                    </div>
                </div>
            `).join('');
        }

        // 更新提示词选择框
        function updatePromptSelects() {
            const writingSelect = document.getElementById('writingPromptSelect');
            const inspirationSelect = document.getElementById('inspirationPromptSelect');

            if (writingSelect) {
                // 写作功能提示词
                const writingPrompts = myPrompts.filter(p => p.type === 'writing');
                writingSelect.innerHTML = '<option value="">请选择提示词...</option>' +
                    writingPrompts.map(p => `<option value="${p.id}">${p.name || p.title}</option>`).join('');
            }

            if (inspirationSelect) {
                // 脑洞创意提示词
                const brainstormPrompts = myPrompts.filter(p => p.type === 'brainstorm');
                inspirationSelect.innerHTML = '<option value="">请选择提示词...</option>' +
                    brainstormPrompts.map(p => `<option value="${p.id}">${p.name || p.title}</option>`).join('');
            }
        }

        // 获取图标
        function getIcon(name) {
            const icons = {
                '长篇': '📚',
                '中篇': '📖',
                '短篇': '📄',
                '剧本': '🎬',
                '全局写作': '✍️',
                '正文': '📝',
                '扩写': '📈',
                '润色': '✨',
                '续写': '➡️',
                '拆书': '📊',
                '起名': '🏷️',
                '其他': '📌',
                '大纲': '📋',
                '卷纲': '📑',
                '章纲': '📃',
                '人设': '👤',
                '灵感创意': '💡',
                '黄金一章': '⭐',
                '书名生成': '📕',
                '简介撰写': '📜',
                '世界观': '🌍',
                '金手指': '🔮',
                '导语': '🎯',
                '加梗润色': '🎭',
                '改写': '🔄',
                '原创': '🌟'
            };
            return icons[name] || '📌';
        }

        // 确认对话框
        function showConfirmDialog(message, onConfirm) {
            const confirmModal = document.createElement('div');
            confirmModal.className = 'prompt-detail-modal';
            confirmModal.innerHTML = `
                <div class="prompt-detail-content" style="max-width: 400px;">
                    <div class="prompt-detail-body" style="padding: 30px; text-align: center;">
                        <div style="font-size: 18px; font-weight: 600; color: var(--text-dark); margin-bottom: 20px;">
                            ${message}
                        </div>
                    </div>
                    <div class="prompt-detail-footer">
                        <button class="prompt-detail-btn cancel" onclick="this.closest('.prompt-detail-modal').remove()">取消</button>
                        <button class="prompt-detail-btn confirm" id="confirmDeleteBtn">确认</button>
                    </div>
                </div>
            `;

            document.body.appendChild(confirmModal);
            confirmModal.style.display = 'flex';

            requestAnimationFrame(() => {
                confirmModal.classList.add('show');
            });

            confirmModal.querySelector('#confirmDeleteBtn').addEventListener('click', () => {
                onConfirm();
                confirmModal.classList.remove('show');
                setTimeout(() => confirmModal.remove(), 300);
            });

            confirmModal.addEventListener('click', (e) => {
                if (e.target === confirmModal) {
                    confirmModal.classList.remove('show');
                    setTimeout(() => confirmModal.remove(), 300);
                }
            });
        }

        // 暴露showPromptDetail到window对象以供onclick使用
        window.showPromptDetail = showPromptDetail;

        // 多选功能 - 修复问题4：支持章节和便签多选
        let selectedChapters = [];
        let selectedMemos = [];
        const MAX_SELECTIONS = 10;

        // 章节多选功能
        window.toggleChapterDropdown = function() {
            const dropdown = document.getElementById('chapter-dropdown');
            const display = document.getElementById('chapter-select-display');

            if (dropdown.classList.contains('show')) {
                dropdown.classList.remove('show');
                display.classList.remove('active');
            } else {
                // 关闭其他下拉框
                document.getElementById('memo-dropdown').classList.remove('show');
                document.getElementById('memo-select-display').classList.remove('active');

                dropdown.classList.add('show');
                display.classList.add('active');
                updateChapterDropdown();
            }
        };

        // 便签多选功能
        window.toggleMemoDropdown = function() {
            const dropdown = document.getElementById('memo-dropdown');
            const display = document.getElementById('memo-select-display');

            if (dropdown.classList.contains('show')) {
                dropdown.classList.remove('show');
                display.classList.remove('active');
            } else {
                // 关闭其他下拉框
                document.getElementById('chapter-dropdown').classList.remove('show');
                document.getElementById('chapter-select-display').classList.remove('active');

                dropdown.classList.add('show');
                display.classList.add('active');
                updateMemoDropdown();
            }
        };

        // 更新章节下拉选项
        function updateChapterDropdown() {
            const dropdown = document.getElementById('chapter-dropdown');
            const chapterItems = document.querySelectorAll('.chapter-item');
            const MAX_SELECTIONS = 10;

            // 按创建时间排序（最新到最旧）- 确保正确的时间戳比较
            const chapters = Array.from(chapterItems).map(item => ({
                id: item.dataset.chapterId,
                title: item.querySelector('.chapter-title-wrapper').textContent.trim(),
                createTime: parseInt(item.dataset.createTime) || 0
            })).sort((a, b) => b.createTime - a.createTime);

            dropdown.innerHTML = chapters.map(chapter => `
                <div class="multi-select-option ${selectedChapters.length >= MAX_SELECTIONS && !selectedChapters.includes(chapter.id) ? 'disabled' : ''}" data-value="${chapter.id}">
                    <input type="checkbox" id="chapter-${chapter.id}" value="${chapter.id}"
                           ${selectedChapters.includes(chapter.id) ? 'checked' : ''}
                           ${selectedChapters.length >= MAX_SELECTIONS && !selectedChapters.includes(chapter.id) ? 'disabled' : ''}
                           onchange="toggleChapterSelection('${chapter.id}', '${chapter.title}')">
                    <label for="chapter-${chapter.id}">${chapter.title}</label>
                </div>
            `).join('');
        }

        // 更新便签下拉选项
        function updateMemoDropdown() {
            const checkboxes = document.querySelectorAll('#memo-dropdown input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                const option = checkbox.closest('.multi-select-option');
                if (selectedMemos.length >= MAX_SELECTIONS && !selectedMemos.includes(checkbox.value)) {
                    checkbox.disabled = true;
                    option.classList.add('disabled');
                } else {
                    checkbox.disabled = false;
                    option.classList.remove('disabled');
                }

                checkbox.checked = selectedMemos.includes(checkbox.value);
            });
        }

        // 切换章节选择
        window.toggleChapterSelection = function(chapterId, chapterTitle) {
            const index = selectedChapters.indexOf(chapterId);

            if (index > -1) {
                selectedChapters.splice(index, 1);
            } else {
                if (selectedChapters.length < MAX_SELECTIONS) {
                    selectedChapters.push(chapterId);
                } else {
                    showNotification(`最多只能选择${MAX_SELECTIONS}个章节`, 'warning');
                    return;
                }
            }

            updateChapterDisplay();
            updateChapterDropdown();
        };

        // 切换便签选择
        window.toggleMemoSelection = function(memoId, memoTitle) {
            const index = selectedMemos.indexOf(memoId);

            if (index > -1) {
                selectedMemos.splice(index, 1);
            } else {
                if (selectedMemos.length < MAX_SELECTIONS) {
                    selectedMemos.push(memoId);
                } else {
                    showNotification(`最多只能选择${MAX_SELECTIONS}个便签`, 'warning');
                    return;
                }
            }

            updateMemoDisplay();
            updateMemoDropdown();
        };

        // 更新章节显示
        function updateChapterDisplay() {
            const display = document.getElementById('chapter-select-display');
            const placeholder = display.querySelector('.multi-select-placeholder');
            const selectedContainer = display.querySelector('.multi-select-selected');

            if (!selectedContainer) {
                const container = document.createElement('div');
                container.className = 'multi-select-selected';
                display.insertBefore(container, display.querySelector('.multi-select-arrow'));
            }

            const container = display.querySelector('.multi-select-selected');

            if (selectedChapters.length === 0) {
                placeholder.style.display = 'block';
                container.innerHTML = '';
            } else {
                placeholder.style.display = 'none';
                container.innerHTML = selectedChapters.map(chapterId => {
                    const chapterItem = document.querySelector(`[data-chapter-id="${chapterId}"]`);
                    const title = chapterItem ? chapterItem.querySelector('.chapter-title-wrapper').textContent.trim() : `章节${chapterId}`;
                    return `
                        <span class="multi-select-tag">
                            ${title}
                            <span class="multi-select-tag-remove" onclick="removeChapterSelection('${chapterId}')">×</span>
                        </span>
                    `;
                }).join('');
            }
        }

        // 更新便签显示
        function updateMemoDisplay() {
            const display = document.getElementById('memo-select-display');
            const placeholder = display.querySelector('.multi-select-placeholder');
            const selectedContainer = display.querySelector('.multi-select-selected');

            if (!selectedContainer) {
                const container = document.createElement('div');
                container.className = 'multi-select-selected';
                display.insertBefore(container, display.querySelector('.multi-select-arrow'));
            }

            const container = display.querySelector('.multi-select-selected');

            if (selectedMemos.length === 0) {
                placeholder.style.display = 'block';
                container.innerHTML = '';
            } else {
                placeholder.style.display = 'none';
                container.innerHTML = selectedMemos.map(memoId => {
                    const memoLabel = document.querySelector(`#memo-${memoId}`).nextElementSibling.textContent;
                    return `
                        <span class="multi-select-tag">
                            ${memoLabel}
                            <span class="multi-select-tag-remove" onclick="removeMemoSelection('${memoId}')">×</span>
                        </span>
                    `;
                }).join('');
            }
        }

        // 移除章节选择
        window.removeChapterSelection = function(chapterId) {
            const index = selectedChapters.indexOf(chapterId);
            if (index > -1) {
                selectedChapters.splice(index, 1);
                updateChapterDisplay();
                updateChapterDropdown();
            }
        };

        // 移除便签选择
        window.removeMemoSelection = function(memoId) {
            const index = selectedMemos.indexOf(memoId);
            if (index > -1) {
                selectedMemos.splice(index, 1);
                updateMemoDisplay();
                updateMemoDropdown();
            }
        };

        // 点击外部关闭下拉框
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.multi-select-container')) {
                document.querySelectorAll('.multi-select-dropdown').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
                document.querySelectorAll('.multi-select-display').forEach(display => {
                    display.classList.remove('active');
                });
            }
        });

        // 便签多选事件绑定
        document.addEventListener('change', function(e) {
            if (e.target.matches('#memo-dropdown input[type="checkbox"]')) {
                const memoId = e.target.value;
                const memoTitle = e.target.nextElementSibling.textContent;
                toggleMemoSelection(memoId, memoTitle);
            }
        });

        // 工具栏功能实现
        let undoStack = [];
        let redoStack = [];
        let currentFontSize = 16;
        let currentFontFamily = 'Microsoft YaHei';

        // 撤回功能
        window.undoAction = function() {
            const activeTextarea = getActiveTextarea();
            if (activeTextarea && undoStack.length > 0) {
                redoStack.push(activeTextarea.value);
                activeTextarea.value = undoStack.pop();
                showNotification('已撤回', 'success');
            } else {
                showNotification('没有可撤回的操作', 'info');
            }
        };

        // 重做功能
        window.redoAction = function() {
            const activeTextarea = getActiveTextarea();
            if (activeTextarea && redoStack.length > 0) {
                undoStack.push(activeTextarea.value);
                activeTextarea.value = redoStack.pop();
                showNotification('已重做', 'success');
            } else {
                showNotification('没有可重做的操作', 'info');
            }
        };

        // 复制功能
        window.copyText = function() {
            const activeTextarea = getActiveTextarea();
            if (activeTextarea) {
                const selectedText = activeTextarea.value.substring(
                    activeTextarea.selectionStart,
                    activeTextarea.selectionEnd
                );
                if (selectedText) {
                    navigator.clipboard.writeText(selectedText).then(() => {
                        showNotification('已复制选中文本', 'success');
                    });
                } else {
                    navigator.clipboard.writeText(activeTextarea.value).then(() => {
                        showNotification('已复制全部内容', 'success');
                    });
                }
            }
        };

        // 智能排版功能
        window.smartFormat = function() {
            const activeTextarea = getActiveTextarea();
            if (activeTextarea) {
                saveToUndoStack(activeTextarea);
                let text = activeTextarea.value;

                // 1. 替换英文引号为中文引号
                text = text.replace(/"/g, '"').replace(/"/g, '"');
                text = text.replace(/'/g, ''').replace(/'/g, ''');

                // 2. 统一段落间距
                text = text.replace(/\n{3,}/g, '\n\n');

                // 3. 去除行首行尾空格
                text = text.split('\n').map(line => line.trim()).join('\n');

                // 4. 标点符号后添加适当空格（中英文混排）
                text = text.replace(/([。！？])([A-Za-z])/g, '$1 $2');
                text = text.replace(/([A-Za-z])([，。！？])/g, '$1$2');

                activeTextarea.value = text;
                showNotification('智能排版完成', 'success');
            }
        };

        // 获取当前活动的文本区域
        function getActiveTextarea() {
            const textareas = [
                document.getElementById('chapterContent'),
                document.getElementById('inputTextarea'),
                document.getElementById('outputTextarea'),
                document.getElementById('followUpInput')
            ];

            for (let textarea of textareas) {
                if (textarea && document.activeElement === textarea) {
                    return textarea;
                }
            }

            // 如果没有焦点，默认返回章节内容
            return document.getElementById('chapterContent');
        }

        // 保存到撤回栈
        function saveToUndoStack(textarea) {
            undoStack.push(textarea.value);
            if (undoStack.length > 50) { // 限制撤回栈大小
                undoStack.shift();
            }
            redoStack = []; // 清空重做栈
        }

        // 字号调节功能
        window.openFontSizeDialog = function() {
            const dialog = document.getElementById('fontSizeDialog');
            const slider = document.getElementById('fontSizeSlider');
            const valueDisplay = document.getElementById('fontSizeValue');

            slider.value = currentFontSize;
            valueDisplay.textContent = currentFontSize + 'px';

            dialog.style.display = 'flex';
            requestAnimationFrame(() => {
                dialog.classList.add('show');
            });
        };

        window.closeFontSizeDialog = function() {
            const dialog = document.getElementById('fontSizeDialog');
            dialog.classList.remove('show');
            setTimeout(() => {
                dialog.style.display = 'none';
            }, 300);
        };

        window.updateFontSize = function(value) {
            document.getElementById('fontSizeValue').textContent = value + 'px';
        };

        window.applyFontSize = function() {
            const value = document.getElementById('fontSizeSlider').value;
            currentFontSize = parseInt(value);

            // 应用到所有文本区域
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                textarea.style.fontSize = currentFontSize + 'px';
            });

            closeFontSizeDialog();
            showNotification(`字号已调整为 ${currentFontSize}px`, 'success');
        };

        // 字体更换功能
        const chineseFonts = [
            { name: 'Microsoft YaHei', display: '微软雅黑' },
            { name: 'SimSun', display: '宋体' },
            { name: 'SimHei', display: '黑体' },
            { name: 'KaiTi', display: '楷体' },
            { name: 'FangSong', display: '仿宋' },
            { name: 'PingFang SC', display: '苹方' },
            { name: 'Noto Sans CJK SC', display: 'Noto Sans' },
            { name: 'Source Han Sans CN', display: '思源黑体' }
        ];

        window.openFontFamilyDialog = function() {
            const dialog = document.getElementById('fontFamilyDialog');
            const grid = document.getElementById('fontFamilyGrid');

            grid.innerHTML = chineseFonts.map(font => `
                <div class="font-family-item ${font.name === currentFontFamily ? 'selected' : ''}"
                     data-font="${font.name}"
                     style="font-family: '${font.name}', sans-serif;"
                     onclick="selectFont('${font.name}')">
                    ${font.display}
                </div>
            `).join('');

            dialog.style.display = 'flex';
            requestAnimationFrame(() => {
                dialog.classList.add('show');
            });
        };

        window.closeFontFamilyDialog = function() {
            const dialog = document.getElementById('fontFamilyDialog');
            dialog.classList.remove('show');
            setTimeout(() => {
                dialog.style.display = 'none';
            }, 300);
        };

        window.selectFont = function(fontName) {
            document.querySelectorAll('.font-family-item').forEach(item => {
                item.classList.remove('selected');
            });
            document.querySelector(`[data-font="${fontName}"]`).classList.add('selected');
        };

        window.applyFontFamily = function() {
            const selected = document.querySelector('.font-family-item.selected');
            if (selected) {
                currentFontFamily = selected.dataset.font;

                // 应用到所有文本区域
                const textareas = document.querySelectorAll('textarea');
                textareas.forEach(textarea => {
                    textarea.style.fontFamily = `'${currentFontFamily}', sans-serif`;
                });

                closeFontFamilyDialog();
                showNotification(`字体已更换为 ${selected.textContent}`, 'success');
            }
        };

        window.handleFontUpload = function(input) {
            const file = input.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const fontName = file.name.replace(/\.[^/.]+$/, "");
                    const fontFace = new FontFace(fontName, e.target.result);

                    fontFace.load().then(function(loadedFont) {
                        document.fonts.add(loadedFont);

                        // 添加到字体列表
                        const grid = document.getElementById('fontFamilyGrid');
                        const newFontItem = document.createElement('div');
                        newFontItem.className = 'font-family-item';
                        newFontItem.dataset.font = fontName;
                        newFontItem.style.fontFamily = fontName;
                        newFontItem.textContent = fontName;
                        newFontItem.onclick = () => selectFont(fontName);
                        grid.appendChild(newFontItem);

                        showNotification(`字体 ${fontName} 上传成功`, 'success');
                    }).catch(function() {
                        showNotification('字体文件格式不支持', 'error');
                    });
                };
                reader.readAsArrayBuffer(file);
            }
        };

        // 搜索功能
        window.openSearchDialog = function() {
            const dialog = document.getElementById('searchDialog');
            dialog.style.display = 'flex';
            requestAnimationFrame(() => {
                dialog.classList.add('show');
                document.getElementById('searchInput').focus();
            });
        };

        window.closeSearchDialog = function() {
            const dialog = document.getElementById('searchDialog');
            dialog.classList.remove('show');
            setTimeout(() => {
                dialog.style.display = 'none';
            }, 300);
        };

        window.performSearch = function() {
            const searchText = document.getElementById('searchInput').value;
            const caseSensitive = document.getElementById('caseSensitive').checked;
            const wholeWord = document.getElementById('wholeWord').checked;

            if (!searchText) {
                showNotification('请输入搜索内容', 'error');
                return;
            }

            const activeTextarea = getActiveTextarea();
            if (activeTextarea) {
                let content = activeTextarea.value;
                let flags = 'g';
                if (!caseSensitive) flags += 'i';

                let pattern = wholeWord ? `\\b${searchText}\\b` : searchText;
                let regex = new RegExp(pattern, flags);
                let matches = content.match(regex);

                if (matches) {
                    showNotification(`找到 ${matches.length} 个匹配项`, 'success');
                    // 高亮第一个匹配项
                    let firstMatch = content.search(regex);
                    if (firstMatch !== -1) {
                        activeTextarea.focus();
                        activeTextarea.setSelectionRange(firstMatch, firstMatch + searchText.length);
                    }
                } else {
                    showNotification('未找到匹配内容', 'info');
                }
            }

            closeSearchDialog();
        };

        // 替换功能
        window.openReplaceDialog = function() {
            const dialog = document.getElementById('replaceDialog');
            dialog.style.display = 'flex';
            requestAnimationFrame(() => {
                dialog.classList.add('show');
                document.getElementById('findInput').focus();
            });
        };

        window.closeReplaceDialog = function() {
            const dialog = document.getElementById('replaceDialog');
            dialog.classList.remove('show');
            setTimeout(() => {
                dialog.style.display = 'none';
            }, 300);
        };

        window.performReplace = function() {
            const findText = document.getElementById('findInput').value;
            const replaceText = document.getElementById('replaceInput').value;
            const caseSensitive = document.getElementById('replaceCaseSensitive').checked;
            const wholeWord = document.getElementById('replaceWholeWord').checked;

            if (!findText) {
                showNotification('请输入查找内容', 'error');
                return;
            }

            const activeTextarea = getActiveTextarea();
            if (activeTextarea) {
                saveToUndoStack(activeTextarea);

                let content = activeTextarea.value;
                let flags = caseSensitive ? 'g' : 'gi';
                let pattern = wholeWord ? `\\b${findText}\\b` : findText;
                let regex = new RegExp(pattern, flags);

                let newContent = content.replace(regex, replaceText);
                let replacedCount = (content.match(regex) || []).length;

                if (replacedCount > 0) {
                    activeTextarea.value = newContent;
                    showNotification(`已替换 ${replacedCount} 处`, 'success');
                } else {
                    showNotification('未找到匹配内容', 'info');
                }
            }

            closeReplaceDialog();
        };

        window.performReplaceAll = function() {
            performReplace(); // 全部替换和单次替换逻辑相同
        };

        // 角色卡管理功能
        let characterCards = [];

        window.openCharacterCardDialog = function() {
            const modal = document.getElementById('characterCardModal');
            modal.style.display = 'flex';
            requestAnimationFrame(() => {
                modal.classList.add('show');
            });
            loadMyCharacters();
        };

        window.closeCharacterCardDialog = function() {
            const modal = document.getElementById('characterCardModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        };

        window.switchCharacterTab = function(tabId) {
            document.querySelectorAll('.modal-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            document.querySelectorAll('.modal-section').forEach(section => {
                section.classList.remove('active');
            });
            document.getElementById(tabId).classList.add('active');
        };

        window.recognizeCharacter = function() {
            const rawText = document.getElementById('characterRawText').value.trim();
            if (!rawText) {
                showNotification('请先输入要识别的文本内容', 'error');
                return;
            }

            // 模拟AI识别过程
            showNotification('AI正在识别中...', 'info');

            setTimeout(() => {
                // 模拟AI识别结果
                const mockResult = {
                    name: '张三',
                    gender: '男',
                    personality: '性格开朗，乐于助人，有强烈的正义感',
                    experience: '出生于普通家庭，通过努力考上名校，现在是一名律师',
                    other: '喜欢运动，特别是篮球；有一个妹妹'
                };

                // 填充AI识别结果弹窗
                document.getElementById('aiCharacterName').value = mockResult.name;
                document.getElementById('aiCharacterGender').value = mockResult.gender;
                document.getElementById('aiCharacterPersonality').value = mockResult.personality;
                document.getElementById('aiCharacterExperience').value = mockResult.experience;
                document.getElementById('aiCharacterOther').value = mockResult.other;

                // 显示AI识别结果弹窗
                const aiModal = document.getElementById('aiRecognitionModal');
                aiModal.style.display = 'flex';
                requestAnimationFrame(() => {
                    aiModal.classList.add('show');
                });

                showNotification('AI识别完成', 'success');
            }, 2000);
        };

        window.closeAiRecognitionModal = function() {
            const modal = document.getElementById('aiRecognitionModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        };

        window.confirmAiRecognition = function() {
            // 将AI识别结果填入创建表单
            document.getElementById('characterName').value = document.getElementById('aiCharacterName').value;
            document.getElementById('characterGender').value = document.getElementById('aiCharacterGender').value;
            document.getElementById('characterPersonality').value = document.getElementById('aiCharacterPersonality').value;
            document.getElementById('characterExperience').value = document.getElementById('aiCharacterExperience').value;
            document.getElementById('characterOther').value = document.getElementById('aiCharacterOther').value;

            closeAiRecognitionModal();
            showNotification('AI识别结果已应用', 'success');
        };

        window.createCharacterCard = function(event) {
            event.preventDefault();

            const name = document.getElementById('characterName').value.trim();
            const gender = document.getElementById('characterGender').value;
            const personality = document.getElementById('characterPersonality').value.trim();
            const experience = document.getElementById('characterExperience').value.trim();
            const other = document.getElementById('characterOther').value.trim();

            if (!name) {
                showNotification('请输入角色名称', 'error');
                return;
            }

            const newCharacter = {
                id: Date.now(),
                name,
                gender,
                personality,
                experience,
                other,
                createTime: new Date().toISOString()
            };

            characterCards.push(newCharacter);
            saveCharacterCards();
            loadMyCharacters();
            resetCharacterForm();

            showNotification('角色卡创建成功', 'success');
        };

        window.resetCharacterForm = function() {
            document.getElementById('characterName').value = '';
            document.getElementById('characterGender').value = '';
            document.getElementById('characterPersonality').value = '';
            document.getElementById('characterExperience').value = '';
            document.getElementById('characterOther').value = '';
            document.getElementById('characterRawText').value = '';
        };

        function loadMyCharacters() {
            const saved = localStorage.getItem('characterCards');
            if (saved) {
                characterCards = JSON.parse(saved);
            }
            renderMyCharacters();
        }

        function saveCharacterCards() {
            localStorage.setItem('characterCards', JSON.stringify(characterCards));
        }

        function renderMyCharacters() {
            const list = document.getElementById('myCharacterList');

            if (characterCards.length === 0) {
                list.innerHTML = '<div style="text-align: center; color: var(--text-light); padding: 40px;">暂无角色卡，请创建新的角色卡</div>';
                return;
            }

            list.innerHTML = characterCards.map((character, index) => `
                <div class="prompt-item" style="--item-index: ${index}">
                    <div class="prompt-info">
                        <div class="prompt-name">${character.name}</div>
                        <div class="prompt-desc">${character.gender} | ${character.personality.substring(0, 50)}${character.personality.length > 50 ? '...' : ''}</div>
                    </div>
                    <div class="prompt-actions">
                        <button class="prompt-action-btn" onclick="selectCharacter(${character.id})" style="background: var(--accent-color);">选择</button>
                        <button class="prompt-action-btn delete" onclick="deleteCharacter(${character.id})">删除</button>
                    </div>
                </div>
            `).join('');
        }

        window.selectCharacter = function(characterId) {
            const character = characterCards.find(c => c.id === characterId);
            if (character) {
                document.getElementById('char-card').value = character.name;
                closeCharacterCardDialog();
                showNotification(`已选择角色：${character.name}`, 'success');
            }
        };

        window.deleteCharacter = function(characterId) {
            showConfirmDialog('确定要删除这个角色卡吗？', () => {
                characterCards = characterCards.filter(c => c.id !== characterId);
                saveCharacterCards();
                renderMyCharacters();
                showNotification('角色卡已删除', 'success');
            });
        };

        // 知识卡管理功能（简化版，类似角色卡）
        window.openKnowledgeCardDialog = function() {
            showNotification('知识卡管理功能开发中...', 'info');
        };

        // 历史记录功能
        let generationHistory = [];

        window.openHistoryDialog = function() {
            const modal = document.getElementById('historyModal');
            modal.style.display = 'flex';
            requestAnimationFrame(() => {
                modal.classList.add('show');
            });
            loadGenerationHistory();
        };

        window.closeHistoryDialog = function() {
            const modal = document.getElementById('historyModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        };

        function loadGenerationHistory() {
            const saved = localStorage.getItem('generationHistory');
            if (saved) {
                generationHistory = JSON.parse(saved);
            } else {
                // 创建一些演示数据
                generationHistory = [
                    {
                        id: 1,
                        title: '第一章：开端',
                        content: '这是一个关于勇气与成长的故事。主人公在面临困境时，选择了勇敢面对...',
                        characterCount: 1250,
                        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1天前
                        type: '章节生成'
                    },
                    {
                        id: 2,
                        title: '角色对话优化',
                        content: '通过智能排版功能，将原本平淡的对话变得更加生动有趣...',
                        characterCount: 890,
                        timestamp: new Date(Date.now() - 43200000).toISOString(), // 12小时前
                        type: '智能排版'
                    },
                    {
                        id: 3,
                        title: '第二章：转折',
                        content: '故事迎来了重要的转折点，主人公发现了隐藏的真相...',
                        characterCount: 1680,
                        timestamp: new Date(Date.now() - 7200000).toISOString(), // 2小时前
                        type: '章节生成'
                    },
                    {
                        id: 4,
                        title: '情节续写',
                        content: '基于前面的内容，继续发展故事情节，增加了更多的悬念和冲突...',
                        characterCount: 2100,
                        timestamp: new Date(Date.now() - 1800000).toISOString(), // 30分钟前
                        type: '继续追问'
                    }
                ];
                saveGenerationHistory();
            }
            renderHistoryStats();
            renderHistoryList();
        }

        function saveGenerationHistory() {
            localStorage.setItem('generationHistory', JSON.stringify(generationHistory));
        }

        function renderHistoryStats() {
            const totalGenerations = generationHistory.length;
            const totalCharacters = generationHistory.reduce((sum, item) => sum + item.characterCount, 0);
            const today = new Date().toDateString();
            const todayGenerations = generationHistory.filter(item =>
                new Date(item.timestamp).toDateString() === today
            ).length;

            document.getElementById('totalGenerations').textContent = totalGenerations;
            document.getElementById('totalCharacters').textContent = totalCharacters.toLocaleString();
            document.getElementById('todayGenerations').textContent = todayGenerations;
        }

        function renderHistoryList() {
            const list = document.getElementById('historyList');

            if (generationHistory.length === 0) {
                list.innerHTML = '<div style="text-align: center; color: var(--text-light); padding: 40px;">暂无生成记录</div>';
                return;
            }

            list.innerHTML = generationHistory
                .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                .map(item => `
                    <div class="history-item" style="border: 1px solid var(--border-color); border-radius: 8px; padding: 16px; margin-bottom: 12px; background: var(--bg-content);">
                        <div style="display: flex; justify-content: between; align-items: flex-start; margin-bottom: 8px;">
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--text-dark); margin-bottom: 4px;">${item.title}</div>
                                <div style="font-size: 12px; color: var(--text-light);">
                                    ${item.type} • ${item.characterCount} 字符 • ${formatTime(item.timestamp)}
                                </div>
                            </div>
                            <button class="btn-secondary" onclick="viewHistoryDetail(${item.id})" style="padding: 4px 8px; font-size: 12px;">查看</button>
                        </div>
                        <div style="color: var(--text-light); font-size: 14px; line-height: 1.4;">
                            ${item.content.substring(0, 100)}${item.content.length > 100 ? '...' : ''}
                        </div>
                    </div>
                `).join('');
        }

        function formatTime(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const diff = now - date;

            if (diff < 60000) return '刚刚';
            if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
            if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
            if (diff < 604800000) return Math.floor(diff / 86400000) + '天前';

            return date.toLocaleDateString();
        }

        window.viewHistoryDetail = function(id) {
            const item = generationHistory.find(h => h.id === id);
            if (item) {
                showNotification(`查看历史记录：${item.title}`, 'info');
                // 这里可以实现详细查看功能
            }
        };

        window.clearHistory = function() {
            showConfirmDialog('确定要清空所有历史记录吗？此操作不可恢复。', () => {
                generationHistory = [];
                saveGenerationHistory();
                renderHistoryStats();
                renderHistoryList();
                showNotification('历史记录已清空', 'success');
            });
        };

        // 添加生成记录的函数（在实际生成内容时调用）
        function addGenerationRecord(title, content, type = '章节生成') {
            const record = {
                id: Date.now(),
                title,
                content,
                characterCount: content.length,
                timestamp: new Date().toISOString(),
                type
            };

            generationHistory.push(record);
            saveGenerationHistory();
        }

        // 更新便签显示
        function updateMemoDisplay() {
            const display = document.getElementById('memo-select-display');
            const placeholder = display.querySelector('.multi-select-placeholder');
            const selectedContainer = display.querySelector('.multi-select-selected');

            if (!selectedContainer) {
                const container = document.createElement('div');
                container.className = 'multi-select-selected';
                display.insertBefore(container, display.querySelector('.multi-select-arrow'));
            }

            const container = display.querySelector('.multi-select-selected');

            if (selectedMemos.length === 0) {
                placeholder.style.display = 'block';
                container.innerHTML = '';
            } else {
                placeholder.style.display = 'none';
                container.innerHTML = selectedMemos.map(memoId => {
                    const memoLabel = document.querySelector(`#memo-${memoId}`).nextElementSibling.textContent;
                    return `
                        <span class="multi-select-tag">
                            ${memoLabel}
                            <span class="multi-select-tag-remove" onclick="removeMemoSelection('${memoId}')">×</span>
                        </span>
                    `;
                }).join('');
            }
        }

        // 移除章节选择
        window.removeChapterSelection = function(chapterId) {
            const index = selectedChapters.indexOf(chapterId);
            if (index > -1) {
                selectedChapters.splice(index, 1);
                updateChapterDisplay();
                updateChapterDropdown();
            }
        };

        // 移除便签选择
        window.removeMemoSelection = function(memoId) {
            const index = selectedMemos.indexOf(memoId);
            if (index > -1) {
                selectedMemos.splice(index, 1);
                updateMemoDisplay();
                updateMemoDropdown();
            }
        };

        // 更新章节选择框
        function updateChapterSelects() {
            updateChapterDropdown();
        }

        // 点击外部关闭下拉框
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.multi-select-container')) {
                document.querySelectorAll('.multi-select-dropdown').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
                document.querySelectorAll('.multi-select-display').forEach(display => {
                    display.classList.remove('active');
                });
            }
        });

        // 便签多选事件绑定
        document.addEventListener('change', (e) => {
            if (e.target.matches('#memo-dropdown input[type="checkbox"]')) {
                const memoId = e.target.value;
                const memoLabel = e.target.nextElementSibling.textContent;
                toggleMemoSelection(memoId, memoLabel);
            }
        });

        // 继续追问功能重复定义已删除，使用前面的定义

        // 自动保存提示
        function showAutoSave() {
            const status = document.querySelector('.auto-save-status');
            status.style.opacity = '1';

            setTimeout(() => {
                status.style.opacity = '0.6';
            }, 2000);
        }

        // 章节内容更新
        function updateChapterContent(chapterId) {
            const chapterItem = document.querySelector(`[data-chapter-id="${chapterId}"]`);
            if (chapterItem) {
                const titleWrapper = chapterItem.querySelector('.chapter-title-wrapper');
                const fullTitle = titleWrapper.textContent.trim();
                document.getElementById('currentChapterTitle').textContent = fullTitle;

                // 这里可以加载对应章节的内容
                const chapterContent = document.getElementById('chapterContent');
                chapterContent.placeholder = `正在编辑：${fullTitle}`;
            }
        }

        // 章节事件绑定
        function bindChapterEvents() {
            const chapterItems = document.querySelectorAll('.chapter-item');
            chapterItems.forEach(item => {
                // 移除旧的事件监听器
                const newItem = item.cloneNode(true);
                item.parentNode.replaceChild(newItem, item);

                // 添加点击事件
                newItem.addEventListener('click', function(e) {
                    // 如果点击的是操作按钮或编辑区域，不触发章节切换
                    if (e.target.closest('.chapter-actions') ||
                        e.target.classList.contains('chapter-number') ||
                        e.target.classList.contains('chapter-number-input')) {
                        return;
                    }

                    // 切换活动章节
                    document.querySelectorAll('.chapter-item').forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                    updateChapterContent(this.dataset.chapterId);
                });
            });
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const textElement = notification.querySelector('.notification-text');
            const iconElement = notification.querySelector('.notification-icon');

            // 设置图标
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            iconElement.textContent = icons[type] || icons.info;
            textElement.textContent = message;

            // 设置样式
            notification.className = `notification ${type}`;
            notification.style.display = 'flex';

            // 自动隐藏
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }

        // 自定义拖拽调整功能已移除 - 拖动条已隐藏
        function initResizeHandles() {
            // 功能已禁用，拖动条已隐藏
            console.log('Resize handles disabled - drag handles are hidden');
        }

        // 测试函数 - 验证JavaScript是否正常工作
        console.log('JavaScript loaded successfully');
        console.log('Available functions:', Object.keys(window).filter(key => typeof window[key] === 'function' && key.startsWith('open')));

        // 添加一个简单的测试函数
        window.testFunction = function() {
            alert('测试函数工作正常！');
        };
    })();
    </script>

    <!-- 字号调节弹窗 -->
    <div class="font-dialog" id="fontSizeDialog">
        <div class="font-dialog-content">
            <div class="font-dialog-header">
                <h3 class="font-dialog-title">字号调节</h3>
                <button class="font-dialog-close" onclick="closeFontSizeDialog()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="font-dialog-body">
                <div class="font-size-controls">
                    <span>小</span>
                    <input type="range" class="font-size-slider" id="fontSizeSlider" min="12" max="24" value="16" oninput="updateFontSize(this.value)">
                    <span>大</span>
                    <span class="font-size-value" id="fontSizeValue">16px</span>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn-primary" onclick="applyFontSize()">应用</button>
                    <button class="btn-cancel" onclick="closeFontSizeDialog()" style="margin-left: 12px;">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 字体更换弹窗 -->
    <div class="font-dialog" id="fontFamilyDialog">
        <div class="font-dialog-content">
            <div class="font-dialog-header">
                <h3 class="font-dialog-title">字体更换</h3>
                <button class="font-dialog-close" onclick="closeFontFamilyDialog()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="font-dialog-body">
                <div class="font-family-grid" id="fontFamilyGrid">
                    <!-- 动态生成字体选项 -->
                </div>
                <div class="font-upload-area" onclick="document.getElementById('fontFileInput').click()">
                    <div>📁 点击上传本地字体文件</div>
                    <div style="font-size: 12px; color: var(--text-light); margin-top: 8px;">支持 .ttf, .otf, .woff, .woff2 格式</div>
                </div>
                <input type="file" id="fontFileInput" accept=".ttf,.otf,.woff,.woff2" style="display: none;" onchange="handleFontUpload(this)">
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn-primary" onclick="applyFontFamily()">应用</button>
                    <button class="btn-cancel" onclick="closeFontFamilyDialog()" style="margin-left: 12px;">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索弹窗 -->
    <div class="search-dialog" id="searchDialog">
        <div class="search-dialog-content">
            <div class="font-dialog-header">
                <h3 class="font-dialog-title">搜索</h3>
                <button class="font-dialog-close" onclick="closeSearchDialog()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="font-dialog-body">
                <div class="form-group">
                    <label class="form-label">搜索内容</label>
                    <input type="text" class="form-input" id="searchInput" placeholder="请输入要搜索的内容...">
                </div>
                <div style="display: flex; gap: 12px; margin-top: 20px;">
                    <label style="display: flex; align-items: center; gap: 6px;">
                        <input type="checkbox" id="caseSensitive"> 区分大小写
                    </label>
                    <label style="display: flex; align-items: center; gap: 6px;">
                        <input type="checkbox" id="wholeWord"> 全词匹配
                    </label>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn-primary" onclick="performSearch()">搜索</button>
                    <button class="btn-cancel" onclick="closeSearchDialog()" style="margin-left: 12px;">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查找替换弹窗 -->
    <div class="search-dialog" id="replaceDialog">
        <div class="search-dialog-content">
            <div class="font-dialog-header">
                <h3 class="font-dialog-title">查找替换</h3>
                <button class="font-dialog-close" onclick="closeReplaceDialog()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="font-dialog-body">
                <div class="form-group">
                    <label class="form-label">查找内容</label>
                    <input type="text" class="form-input" id="findInput" placeholder="请输入要查找的内容...">
                </div>
                <div class="form-group">
                    <label class="form-label">替换为</label>
                    <input type="text" class="form-input" id="replaceInput" placeholder="请输入替换后的内容...">
                </div>
                <div style="display: flex; gap: 12px; margin-top: 20px;">
                    <label style="display: flex; align-items: center; gap: 6px;">
                        <input type="checkbox" id="replaceCaseSensitive"> 区分大小写
                    </label>
                    <label style="display: flex; align-items: center; gap: 6px;">
                        <input type="checkbox" id="replaceWholeWord"> 全词匹配
                    </label>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn-primary" onclick="performReplace()">替换</button>
                    <button class="btn-secondary" onclick="performReplaceAll()" style="margin-left: 8px;">全部替换</button>
                    <button class="btn-cancel" onclick="closeReplaceDialog()" style="margin-left: 8px;">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 角色卡管理弹窗 -->
    <div class="modal" id="characterCardModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">角色卡管理</h2>
                <button class="modal-close" onclick="closeCharacterCardDialog()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="modal-tabs">
                    <div class="modal-tab active" onclick="switchCharacterTab('my-characters')">我的角色卡</div>
                    <div class="modal-tab" onclick="switchCharacterTab('create-character')">新建角色卡</div>
                </div>

                <!-- 我的角色卡 -->
                <div class="modal-section active" id="my-characters">
                    <div class="prompt-list" id="myCharacterList">
                        <!-- 动态生成 -->
                    </div>
                </div>

                <!-- 新建角色卡 -->
                <div class="modal-section" id="create-character">
                    <form class="create-prompt-form" onsubmit="createCharacterCard(event)">
                        <div class="form-group">
                            <label class="form-label">角色名称</label>
                            <input type="text" class="form-input" id="characterName" placeholder="请输入角色名称..." required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">AI识别内容 (可选)</label>
                            <textarea class="form-textarea" id="characterRawText" placeholder="粘贴角色相关文本，AI将自动识别并提取角色信息..." style="min-height: 120px;"></textarea>
                            <button type="button" class="btn-secondary" onclick="recognizeCharacter()" style="margin-top: 8px;">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                                AI识别
                            </button>
                        </div>

                        <div class="form-group">
                            <label class="form-label">性别</label>
                            <select class="form-input" id="characterGender">
                                <option value="">请选择性别</option>
                                <option value="男">男</option>
                                <option value="女">女</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">性格特点</label>
                            <textarea class="form-textarea" id="characterPersonality" placeholder="描述角色的性格特点..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">经历背景</label>
                            <textarea class="form-textarea" id="characterExperience" placeholder="描述角色的经历和背景..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">其他信息</label>
                            <textarea class="form-textarea" id="characterOther" placeholder="其他相关信息..."></textarea>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-primary">创建角色卡</button>
                            <button type="button" class="btn-cancel" onclick="resetCharacterForm()">重置</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- AI识别结果弹窗 -->
    <div class="modal" id="aiRecognitionModal">
        <div class="modal-content" style="max-width: 700px;">
            <div class="modal-header">
                <h2 class="modal-title">AI识别结果</h2>
                <button class="modal-close" onclick="closeAiRecognitionModal()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">角色名称</label>
                    <input type="text" class="form-input" id="aiCharacterName">
                </div>
                <div class="form-group">
                    <label class="form-label">性别</label>
                    <input type="text" class="form-input" id="aiCharacterGender">
                </div>
                <div class="form-group">
                    <label class="form-label">性格特点</label>
                    <textarea class="form-textarea" id="aiCharacterPersonality"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">经历背景</label>
                    <textarea class="form-textarea" id="aiCharacterExperience"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">其他信息</label>
                    <textarea class="form-textarea" id="aiCharacterOther"></textarea>
                </div>
                <div class="form-actions">
                    <button class="btn-primary" onclick="confirmAiRecognition()">确认使用</button>
                    <button class="btn-cancel" onclick="closeAiRecognitionModal()">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史记录弹窗 -->
    <div class="modal" id="historyModal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h2 class="modal-title">历史记录</h2>
                <button class="modal-close" onclick="closeHistoryDialog()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="history-stats" style="display: flex; gap: 20px; margin-bottom: 20px; padding: 16px; background: var(--bg-panel-secondary); border-radius: 8px;">
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: 600; color: var(--primary-color);" id="totalGenerations">0</div>
                        <div style="font-size: 12px; color: var(--text-light);">总生成次数</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: 600; color: var(--accent-color);" id="totalCharacters">0</div>
                        <div style="font-size: 12px; color: var(--text-light);">总字符消耗</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: 600; color: var(--success-color);" id="todayGenerations">0</div>
                        <div style="font-size: 12px; color: var(--text-light);">今日生成</div>
                    </div>
                </div>

                <div class="history-list" id="historyList" style="max-height: 400px; overflow-y: auto;">
                    <!-- 动态生成历史记录 -->
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn-secondary" onclick="clearHistory()">清空历史记录</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

